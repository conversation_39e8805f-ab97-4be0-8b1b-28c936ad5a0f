{
  "common": {
    "save": "<PERSON>ar",
    "cancel": "Cancelar",
    "delete": "Eliminar",
    "edit": "Editar",
    "add": "Agregar",
    "search": "Buscar",
    "filter": "Filtrar",
    "export": "Exportar",
    "import": "Importar",
    "loading": "Cargando...",
    "error": "Error",
    "success": "Éxito",
    "warning": "Advertencia",
    "info": "Información",
    "confirm": "Confirmar",
    "yes": "Sí",
    "no": "No",
    "ok": "Aceptar",
    "close": "Cerrar",
    "back": "Volver",
    "next": "Siguiente",
    "previous": "Anterior",
    "select": "Seleccionar",
    "clear": "Limpiar",
    "reset": "Restablecer",
    "refresh": "Actualizar",
    "view": "Ver",
    "print": "Imprimir",
    "download": "Descargar",
    "upload": "Subir",
    "copy": "Copiar",
    "paste": "Pegar",
    "cut": "Cortar",
    "undo": "Deshacer",
    "redo": "Rehacer",
    "required": "Requerido",
    "optional": "Opcional",
    "total": "Total",
    "subtotal": "Subtotal",
    "quantity": "Cantidad",
    "price": "Precio",
    "amount": "Importe",
    "date": "Fecha",
    "time": "Hora",
    "name": "Nombre",
    "description": "Descripción",
    "status": "Estado",
    "active": "Activo",
    "inactive": "Inactivo",
    "enabled": "Habilitado",
    "disabled": "Deshabilitado"
  },
  "navigation": {
    "dashboard": "Panel de Control",
    "products": "Productos",
    "inventory": "Inventario",
    "sales": "Ventas",
    "purchases": "Compras",
    "cash": "Caja",
    "reports": "Reportes",
    "settings": "Configuración",
    "profile": "Perfil",
    "logout": "Cerrar Sesión"
  },
  "auth": {
    "login": "Iniciar Sesión",
    "logout": "Cerrar Sesión",
    "email": "Correo Electrónico",
    "password": "Contraseña",
    "forgotPassword": "¿Olvidaste tu contraseña?",
    "rememberMe": "Recordarme",
    "signIn": "Ingresar",
    "signUp": "Registrarse",
    "createAccount": "Crear Cuenta",
    "welcomeBack": "¡Bienvenido de vuelta!",
    "invalidCredentials": "Credenciales inválidas",
    "accountCreated": "Cuenta creada exitosamente"
  },
  "dashboard": {
    "title": "Panel de Control",
    "welcome": "Bienvenido, {name}",
    "overview": "Resumen General",
    "todaySales": "Ventas de Hoy",
    "totalRevenue": "Ingresos Totales",
    "lowStock": "Stock Bajo",
    "reorderNeeded": "Necesita Reposición",
    "salesChart": "Gráfico de Ventas",
    "topProducts": "Productos Más Vendidos",
    "recentTransactions": "Transacciones Recientes",
    "cashFlow": "Flujo de Caja",
    "profitMargin": "Margen de Ganancia",
    "inventoryValue": "Valor del Inventario",
    "pendingOrders": "Pedidos Pendientes"
  },
  "products": {
    "title": "Productos",
    "addProduct": "Agregar Producto",
    "editProduct": "Editar Producto",
    "deleteProduct": "Eliminar Producto",
    "productList": "Lista de Productos",
    "sku": "SKU",
    "barcode": "Código de Barras",
    "category": "Categoría",
    "cost": "Costo",
    "retailPrice": "Precio Minorista",
    "wholesalePrice": "Precio Mayorista",
    "currentStock": "Stock Actual",
    "minStock": "Stock Mínimo",
    "reorderPoint": "Punto de Reposición",
    "variants": "Variantes",
    "addVariant": "Agregar Variante",
    "variantType": "Tipo de Variante",
    "variantValue": "Valor de Variante",
    "size": "Tamaño",
    "color": "Color",
    "material": "Material",
    "brand": "Marca",
    "supplier": "Proveedor",
    "costMethod": "Método de Costo",
    "avgCost": "Costo Promedio",
    "fifo": "PEPS (Primero en Entrar, Primero en Salir)",
    "lifo": "UEPS (Último en Entrar, Primero en Salir)",
    "productCreated": "Producto creado exitosamente",
    "productUpdated": "Producto actualizado exitosamente",
    "productDeleted": "Producto eliminado exitosamente",
    "confirmDelete": "¿Estás seguro de que quieres eliminar este producto?",
    "bulkImport": "Importación Masiva",
    "csvTemplate": "Plantilla CSV",
    "importSuccess": "Importación completada exitosamente",
    "importError": "Error en la importación"
  },
  "inventory": {
    "title": "Inventario",
    "stockMovements": "Movimientos de Stock",
    "adjustment": "Ajuste",
    "transfer": "Transferencia",
    "lowStockAlert": "Alerta de Stock Bajo",
    "reorderAlert": "Alerta de Reposición",
    "stockValue": "Valor del Stock",
    "turnoverRate": "Tasa de Rotación",
    "deadStock": "Stock Muerto",
    "fastMoving": "Movimiento Rápido",
    "slowMoving": "Movimiento Lento",
    "outOfStock": "Sin Stock",
    "inStock": "En Stock",
    "reserved": "Reservado",
    "available": "Disponible",
    "onOrder": "En Pedido",
    "damaged": "Dañado",
    "expired": "Vencido",
    "returned": "Devuelto"
  },
  "sales": {
    "title": "Ventas",
    "newSale": "Nueva Venta",
    "saleNumber": "Número de Venta",
    "customer": "Cliente",
    "customerName": "Nombre del Cliente",
    "saleDate": "Fecha de Venta",
    "items": "Artículos",
    "addItem": "Agregar Artículo",
    "unitPrice": "Precio Unitario",
    "totalPrice": "Precio Total",
    "discount": "Descuento",
    "tax": "Impuesto",
    "grandTotal": "Total General",
    "paymentMethod": "Método de Pago",
    "cash": "Efectivo",
    "card": "Tarjeta",
    "transfer": "Transferencia",
    "credit": "Crédito",
    "receipt": "Recibo",
    "printReceipt": "Imprimir Recibo",
    "emailReceipt": "Enviar Recibo por Email",
    "whatsappReceipt": "Enviar Recibo por WhatsApp",
    "saleCompleted": "Venta completada exitosamente",
    "saleVoided": "Venta anulada",
    "refund": "Reembolso",
    "partialRefund": "Reembolso Parcial",
    "fullRefund": "Reembolso Total",
    "dailySales": "Ventas Diarias",
    "weeklySales": "Ventas Semanales",
    "monthlySales": "Ventas Mensuales",
    "salesReport": "Reporte de Ventas",
    "topSellingProducts": "Productos Más Vendidos",
    "salesByCategory": "Ventas por Categoría",
    "salesTrend": "Tendencia de Ventas"
  },
  "purchases": {
    "title": "Compras",
    "newPurchase": "Nueva Compra",
    "purchaseOrder": "Orden de Compra",
    "purchaseNumber": "Número de Compra",
    "supplier": "Proveedor",
    "supplierName": "Nombre del Proveedor",
    "purchaseDate": "Fecha de Compra",
    "deliveryDate": "Fecha de Entrega",
    "unitCost": "Costo Unitario",
    "totalCost": "Costo Total",
    "received": "Recibido",
    "pending": "Pendiente",
    "cancelled": "Cancelado",
    "purchaseCreated": "Compra creada exitosamente",
    "purchaseUpdated": "Compra actualizada exitosamente",
    "receiveItems": "Recibir Artículos",
    "partialReceive": "Recepción Parcial",
    "fullReceive": "Recepción Completa",
    "purchaseReport": "Reporte de Compras",
    "supplierReport": "Reporte de Proveedores",
    "costAnalysis": "Análisis de Costos"
  },
  "cash": {
    "title": "Caja",
    "cashClosing": "Cierre de Caja",
    "openingCash": "Efectivo de Apertura",
    "closingCash": "Efectivo de Cierre",
    "totalSales": "Total de Ventas",
    "totalExpenses": "Total de Gastos",
    "cashDifference": "Diferencia de Efectivo",
    "expectedCash": "Efectivo Esperado",
    "actualCash": "Efectivo Real",
    "shortage": "Faltante",
    "overage": "Sobrante",
    "notes": "Notas",
    "approved": "Aprobado",
    "pending": "Pendiente",
    "rejected": "Rechazado",
    "cashFlow": "Flujo de Caja",
    "dailyCash": "Caja Diaria",
    "weeklyCash": "Caja Semanal",
    "monthlyCash": "Caja Mensual",
    "cashReport": "Reporte de Caja",
    "expense": "Gasto",
    "income": "Ingreso",
    "expenseType": "Tipo de Gasto",
    "addExpense": "Agregar Gasto",
    "expenseAmount": "Monto del Gasto",
    "expenseDescription": "Descripción del Gasto"
  },
  "reports": {
    "title": "Reportes",
    "salesReport": "Reporte de Ventas",
    "inventoryReport": "Reporte de Inventario",
    "purchaseReport": "Reporte de Compras",
    "cashReport": "Reporte de Caja",
    "profitLoss": "Pérdidas y Ganancias",
    "balanceSheet": "Balance General",
    "dateRange": "Rango de Fechas",
    "fromDate": "Desde",
    "toDate": "Hasta",
    "generateReport": "Generar Reporte",
    "exportPdf": "Exportar PDF",
    "exportCsv": "Exportar CSV",
    "exportExcel": "Exportar Excel",
    "csvStandard": "CSV Estándar (,)",
    "csvExcelAr": "CSV Excel Argentina (;)",
    "reportGenerated": "Reporte generado exitosamente",
    "noDataFound": "No se encontraron datos para el período seleccionado",
    "summary": "Resumen",
    "details": "Detalles",
    "charts": "Gráficos",
    "kpi": "Indicadores Clave",
    "performance": "Rendimiento",
    "trends": "Tendencias",
    "comparison": "Comparación",
    "forecast": "Pronóstico"
  },
  "settings": {
    "title": "Configuración",
    "profile": "Perfil",
    "account": "Cuenta",
    "preferences": "Preferencias",
    "language": "Idioma",
    "timezone": "Zona Horaria",
    "currency": "Moneda",
    "dateFormat": "Formato de Fecha",
    "timeFormat": "Formato de Hora",
    "numberFormat": "Formato de Números",
    "notifications": "Notificaciones",
    "emailNotifications": "Notificaciones por Email",
    "pushNotifications": "Notificaciones Push",
    "smsNotifications": "Notificaciones SMS",
    "security": "Seguridad",
    "changePassword": "Cambiar Contraseña",
    "twoFactorAuth": "Autenticación de Dos Factores",
    "loginHistory": "Historial de Inicios de Sesión",
    "backup": "Respaldo",
    "dataExport": "Exportar Datos",
    "dataImport": "Importar Datos",
    "systemSettings": "Configuración del Sistema",
    "userManagement": "Gestión de Usuarios",
    "rolePermissions": "Permisos de Roles",
    "integrations": "Integraciones",
    "apiKeys": "Claves API",
    "webhooks": "Webhooks",
    "settingsSaved": "Configuración guardada exitosamente"
  },
  "errors": {
    "general": "Ha ocurrido un error inesperado",
    "network": "Error de conexión de red",
    "validation": "Error de validación",
    "unauthorized": "No autorizado",
    "forbidden": "Acceso prohibido",
    "notFound": "No encontrado",
    "serverError": "Error interno del servidor",
    "timeout": "Tiempo de espera agotado",
    "invalidInput": "Entrada inválida",
    "duplicateEntry": "Entrada duplicada",
    "insufficientStock": "Stock insuficiente",
    "invalidBarcode": "Código de barras inválido",
    "invalidSku": "SKU inválido",
    "priceRequired": "El precio es requerido",
    "quantityRequired": "La cantidad es requerida",
    "customerRequired": "El cliente es requerido",
    "productRequired": "El producto es requerido",
    "dateRequired": "La fecha es requerida",
    "amountRequired": "El monto es requerido"
  },
  "validation": {
    "required": "Este campo es requerido",
    "email": "Ingrese un email válido",
    "minLength": "Mínimo {min} caracteres",
    "maxLength": "Máximo {max} caracteres",
    "min": "Valor mínimo: {min}",
    "max": "Valor máximo: {max}",
    "positive": "Debe ser un número positivo",
    "integer": "Debe ser un número entero",
    "decimal": "Debe ser un número decimal válido",
    "unique": "Este valor ya existe",
    "pattern": "Formato inválido",
    "date": "Ingrese una fecha válida",
    "time": "Ingrese una hora válida",
    "url": "Ingrese una URL válida",
    "phone": "Ingrese un número de teléfono válido"
  },
  "units": {
    "piece": "Unidad",
    "pieces": "Unidades",
    "kg": "kg",
    "g": "g",
    "l": "l",
    "ml": "ml",
    "m": "m",
    "cm": "cm",
    "mm": "mm",
    "box": "Caja",
    "boxes": "Cajas",
    "pack": "Paquete",
    "packs": "Paquetes",
    "dozen": "Docena",
    "dozens": "Docenas"
  },
  "time": {
    "now": "Ahora",
    "today": "Hoy",
    "yesterday": "Ayer",
    "tomorrow": "Mañana",
    "thisWeek": "Esta Semana",
    "lastWeek": "Semana Pasada",
    "nextWeek": "Próxima Semana",
    "thisMonth": "Este Mes",
    "lastMonth": "Mes Pasado",
    "nextMonth": "Próximo Mes",
    "thisYear": "Este Año",
    "lastYear": "Año Pasado",
    "nextYear": "Próximo Año",
    "minute": "minuto",
    "minutes": "minutos",
    "hour": "hora",
    "hours": "horas",
    "day": "día",
    "days": "días",
    "week": "semana",
    "weeks": "semanas",
    "month": "mes",
    "months": "meses",
    "year": "año",
    "years": "años",
    "ago": "hace",
    "in": "en"
  },
  "kpi": {
    "salesTotal": "Total de Ventas",
    "salesGrowth": "Crecimiento de Ventas",
    "profitMargin": "Margen de Ganancia",
    "inventoryTurnover": "Rotación de Inventario",
    "averageOrderValue": "Valor Promedio de Pedido",
    "customerRetention": "Retención de Clientes",
    "costOfGoodsSold": "Costo de Mercadería Vendida",
    "grossProfit": "Ganancia Bruta",
    "netProfit": "Ganancia Neta",
    "returnOnInvestment": "Retorno de Inversión",
    "cashFlowPositive": "Flujo de Caja Positivo",
    "cashFlowNegative": "Flujo de Caja Negativo",
    "stockoutRate": "Tasa de Desabastecimiento",
    "fillRate": "Tasa de Cumplimiento",
    "leadTime": "Tiempo de Entrega",
    "orderAccuracy": "Precisión de Pedidos",
    "customerSatisfaction": "Satisfacción del Cliente",
    "employeeProductivity": "Productividad del Empleado"
  },
  "charts": {
    "salesOverTime": "Ventas en el Tiempo",
    "topProducts": "Productos Principales",
    "categoryBreakdown": "Desglose por Categoría",
    "monthlyTrends": "Tendencias Mensuales",
    "quarterlyComparison": "Comparación Trimestral",
    "yearOverYear": "Año tras Año",
    "dailyPerformance": "Rendimiento Diario",
    "weeklyAnalysis": "Análisis Semanal",
    "profitAnalysis": "Análisis de Ganancias",
    "costAnalysis": "Análisis de Costos",
    "inventoryLevels": "Niveles de Inventario",
    "stockMovements": "Movimientos de Stock",
    "salesForecast": "Pronóstico de Ventas",
    "demandForecast": "Pronóstico de Demanda",
    "seasonalTrends": "Tendencias Estacionales",
    "performanceMetrics": "Métricas de Rendimiento"
  }
}
}
