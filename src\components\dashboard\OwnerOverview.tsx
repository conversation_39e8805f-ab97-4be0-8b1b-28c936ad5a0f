'use client';

import { useTranslations } from 'next-intl';
import { CalendarIcon, PrinterIcon } from '@heroicons/react/24/outline';

import { KPIGrid, type KPIData } from './KPICard';
import { SalesChart, TopProductsChart } from './LocalizedChart';
import { LocalizedDate } from '@/components/ui/LocalizedDate';

interface OwnerOverviewProps {
  date: Date;
  onDateChange: (date: Date) => void;
  onExportPDF: () => void;
  kpis: KPIData[];
  salesData: Array<{
    date: string;
    sales: number;
    target?: number;
  }>;
  topProducts: Array<{
    name: string;
    sales: number;
  }>;
  isLoading?: boolean;
}

export function OwnerOverview({
  date,
  onDateChange,
  onExportPDF,
  kpis,
  salesData,
  topProducts,
  isLoading = false,
}: OwnerOverviewProps) {
  const t = useTranslations('dashboard');
  const tCommon = useTranslations('common');

  // Filter KPIs for owner overview
  const ownerKPIs = kpis.filter(kpi => 
    ['sales_total', 'gross_profit', 'cash_flow_positive', 'inventory_turnover'].includes(kpi.id)
  );

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-80 bg-gray-200 rounded"></div>
            <div className="h-80 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6" data-testid="owner-overview">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {t('title')}
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            {t('overview')} - <LocalizedDate date={date} type="date" format="long" />
          </p>
        </div>
        
        <div className="mt-4 sm:mt-0 flex space-x-3">
          {/* Date Picker */}
          <div className="relative">
            <LocalizedDateInput
              value={date}
              onChange={(newDate) => newDate && onDateChange(newDate)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            />
            <CalendarIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
          </div>
          
          {/* Export PDF Button */}
          <button
            onClick={onExportPDF}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            data-testid="export-pdf"
          >
            <PrinterIcon className="h-4 w-4 mr-2" />
            {tCommon('exportPdf')}
          </button>
        </div>
      </div>

      {/* KPI Cards */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          {t('todaysSummary')}
        </h2>
        <KPIGrid 
          kpis={ownerKPIs} 
          columns={4}
          data-testid="kpi-grid"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Chart */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <SalesChart
            data={salesData}
            title={t('salesChart')}
            height={300}
            data-testid="sales-chart"
          />
        </div>

        {/* Top Products */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <TopProductsChart
            data={topProducts}
            title={t('topProducts')}
            height={300}
            data-testid="top-products-chart"
          />
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {t('quickActions')}
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <QuickActionCard
            title={t('newSale')}
            description={t('createNewSale')}
            href="/sales/new"
            icon="💰"
          />
          <QuickActionCard
            title={t('receiveStock')}
            description={t('receiveNewStock')}
            href="/purchases/receive"
            icon="📦"
          />
          <QuickActionCard
            title={t('cashClosing')}
            description={t('performCashClosing')}
            href="/cash/closing"
            icon="💵"
          />
          <QuickActionCard
            title={t('viewReports')}
            description={t('accessDetailedReports')}
            href="/reports"
            icon="📊"
          />
        </div>
      </div>

      {/* Alerts */}
      {kpis.some(kpi => kpi.isStale) && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                {t('dataStaleWarning')}
              </h3>
              <p className="mt-1 text-sm text-yellow-700">
                {t('dataStaleDescription')}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

interface QuickActionCardProps {
  title: string;
  description: string;
  href: string;
  icon: string;
}

function QuickActionCard({ title, description, href, icon }: QuickActionCardProps) {
  return (
    <a
      href={href}
      className="block p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-sm transition-all duration-200"
    >
      <div className="flex items-center">
        <span className="text-2xl mr-3">{icon}</span>
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900 truncate">
            {title}
          </h4>
          <p className="text-xs text-gray-500 mt-1">
            {description}
          </p>
        </div>
      </div>
    </a>
  );
}

// Import the LocalizedDateInput component
function LocalizedDateInput({ value, onChange, className }: {
  value: Date;
  onChange: (date: Date | null) => void;
  className?: string;
}) {
  const inputValue = value.toISOString().split('T')[0];

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = new Date(event.target.value);
    if (!isNaN(newDate.getTime())) {
      onChange(newDate);
    }
  };

  return (
    <input
      type="date"
      value={inputValue}
      onChange={handleChange}
      className={className}
    />
  );
}
