#!/usr/bin/env node

/**
 * Pseudolocalization Script
 * Generates a pseudolocale (en-XA) for testing i18n implementation
 * This helps identify:
 * - Hardcoded strings
 * - Layout issues with longer text
 * - Missing translations
 * - Text truncation problems
 */

const fs = require('fs');
const path = require('path');

// Pseudolocalization configuration
const PSEUDO_CONFIG = {
  // Character mappings for pseudolocalization
  charMap: {
    'a': 'ä', 'A': 'Ä',
    'e': 'ë', 'E': 'Ë',
    'i': 'ï', 'I': 'Ï',
    'o': 'ö', 'O': 'Ö',
    'u': 'ü', 'U': 'Ü',
    'c': 'ç', 'C': 'Ç',
    'n': 'ñ', 'N': 'Ñ',
    's': 'š', 'S': 'Š',
    'z': 'ž', 'Z': 'Ž',
  },
  
  // Prefix and suffix to identify pseudolocalized text
  prefix: '[!!',
  suffix: '!!]',
  
  // Expansion factor (text becomes 30-40% longer)
  expansionFactor: 0.35,
  
  // Characters to add for expansion
  expansionChars: ['ë', 'ñ', 'ü', 'ö', 'ä', 'ï', 'ç'],
};

/**
 * Transform a single string to pseudolocale
 */
function pseudolocalizeString(str) {
  if (!str || typeof str !== 'string') {
    return str;
  }
  
  // Don't pseudolocalize very short strings or technical strings
  if (str.length < 3 || /^[A-Z_]+$/.test(str) || /^\d+$/.test(str)) {
    return str;
  }
  
  // Don't pseudolocalize strings that look like keys or technical values
  if (/^[a-z][a-zA-Z0-9.]*$/.test(str) || str.includes('.') && str.split('.').length > 2) {
    return str;
  }
  
  // Don't pseudolocalize URLs, emails, or file paths
  if (/^https?:\/\/|@|\/|\\/.test(str)) {
    return str;
  }
  
  let result = str;
  
  // Apply character mappings
  for (const [original, replacement] of Object.entries(PSEUDO_CONFIG.charMap)) {
    result = result.replace(new RegExp(original, 'g'), replacement);
  }
  
  // Add expansion characters
  const expansionLength = Math.floor(str.length * PSEUDO_CONFIG.expansionFactor);
  const expansionText = Array(expansionLength)
    .fill(0)
    .map(() => PSEUDO_CONFIG.expansionChars[Math.floor(Math.random() * PSEUDO_CONFIG.expansionChars.length)])
    .join('');
  
  // Add prefix, original transformed text, expansion, and suffix
  return `${PSEUDO_CONFIG.prefix}${result}${expansionText}${PSEUDO_CONFIG.suffix}`;
}

/**
 * Recursively pseudolocalize an object
 */
function pseudolocalizeObject(obj) {
  if (typeof obj === 'string') {
    return pseudolocalizeString(obj);
  }
  
  if (Array.isArray(obj)) {
    return obj.map(pseudolocalizeObject);
  }
  
  if (obj && typeof obj === 'object') {
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = pseudolocalizeObject(value);
    }
    return result;
  }
  
  return obj;
}

/**
 * Generate pseudolocale file
 */
function generatePseudolocale(sourceLocale = 'en-US') {
  const sourceFile = path.join(__dirname, '..', 'locales', `${sourceLocale}.json`);
  const targetFile = path.join(__dirname, '..', 'locales', 'en-XA.json');
  
  try {
    // Read source locale file
    const sourceContent = fs.readFileSync(sourceFile, 'utf8');
    const sourceData = JSON.parse(sourceContent);
    
    // Generate pseudolocalized version
    const pseudoData = pseudolocalizeObject(sourceData);
    
    // Add metadata
    pseudoData._meta = {
      locale: 'en-XA',
      name: 'Pseudolocale (Testing)',
      description: 'Pseudolocalized version for testing i18n implementation',
      baseLocale: sourceLocale,
      generated: new Date().toISOString(),
      config: PSEUDO_CONFIG,
    };
    
    // Write pseudolocale file
    fs.writeFileSync(targetFile, JSON.stringify(pseudoData, null, 2), 'utf8');
    
    console.log('✅ Pseudolocale generated successfully!');
    console.log(`📁 Source: ${sourceFile}`);
    console.log(`📁 Target: ${targetFile}`);
    console.log(`📊 Strings processed: ${countStrings(sourceData)}`);
    
    return targetFile;
  } catch (error) {
    console.error('❌ Error generating pseudolocale:', error.message);
    process.exit(1);
  }
}

/**
 * Count total number of strings in locale object
 */
function countStrings(obj) {
  let count = 0;
  
  function traverse(item) {
    if (typeof item === 'string') {
      count++;
    } else if (Array.isArray(item)) {
      item.forEach(traverse);
    } else if (item && typeof item === 'object') {
      Object.values(item).forEach(traverse);
    }
  }
  
  traverse(obj);
  return count;
}

/**
 * Validate pseudolocale implementation
 */
function validatePseudolocale() {
  const pseudoFile = path.join(__dirname, '..', 'locales', 'en-XA.json');
  
  if (!fs.existsSync(pseudoFile)) {
    console.log('❌ Pseudolocale file not found. Run generation first.');
    return false;
  }
  
  try {
    const content = fs.readFileSync(pseudoFile, 'utf8');
    const data = JSON.parse(content);
    
    console.log('🔍 Validating pseudolocale...');
    
    // Check if all strings are pseudolocalized
    let totalStrings = 0;
    let pseudolocalizedStrings = 0;
    
    function checkStrings(obj, path = '') {
      if (typeof obj === 'string') {
        totalStrings++;
        if (obj.includes(PSEUDO_CONFIG.prefix) && obj.includes(PSEUDO_CONFIG.suffix)) {
          pseudolocalizedStrings++;
        } else {
          console.log(`⚠️  Non-pseudolocalized string at ${path}: "${obj}"`);
        }
      } else if (Array.isArray(obj)) {
        obj.forEach((item, index) => checkStrings(item, `${path}[${index}]`));
      } else if (obj && typeof obj === 'object') {
        Object.entries(obj).forEach(([key, value]) => {
          if (key !== '_meta') { // Skip metadata
            checkStrings(value, path ? `${path}.${key}` : key);
          }
        });
      }
    }
    
    checkStrings(data);
    
    const percentage = totalStrings > 0 ? (pseudolocalizedStrings / totalStrings * 100).toFixed(1) : 0;
    
    console.log(`📊 Validation Results:`);
    console.log(`   Total strings: ${totalStrings}`);
    console.log(`   Pseudolocalized: ${pseudolocalizedStrings} (${percentage}%)`);
    
    if (percentage >= 95) {
      console.log('✅ Pseudolocale validation passed!');
      return true;
    } else {
      console.log('❌ Pseudolocale validation failed. Some strings are not pseudolocalized.');
      return false;
    }
  } catch (error) {
    console.error('❌ Error validating pseudolocale:', error.message);
    return false;
  }
}

/**
 * Generate test report for pseudolocale
 */
function generateTestReport() {
  console.log('\n📋 Pseudolocale Testing Guide:');
  console.log('\n1. 🔧 Setup:');
  console.log('   - Add "en-XA" to your locale switcher for testing');
  console.log('   - Update your i18n configuration to include en-XA');
  console.log('\n2. 🧪 What to test:');
  console.log('   - All text should be wrapped with [!! ... !!]');
  console.log('   - Text should be 30-40% longer than original');
  console.log('   - Layout should not break with longer text');
  console.log('   - No hardcoded strings should appear');
  console.log('\n3. 🚨 Red flags to look for:');
  console.log('   - Text without [!! !!] markers (hardcoded strings)');
  console.log('   - Text overflow or layout breaking');
  console.log('   - Truncated text with "..."');
  console.log('   - Missing translations (empty strings)');
  console.log('\n4. 📱 Test on different screen sizes:');
  console.log('   - Mobile (320px width)');
  console.log('   - Tablet (768px width)');
  console.log('   - Desktop (1024px+ width)');
  console.log('\n5. 🎯 Focus areas:');
  console.log('   - Navigation menus');
  console.log('   - Form labels and buttons');
  console.log('   - Error messages');
  console.log('   - Dashboard KPIs and charts');
  console.log('   - Modal dialogs');
  console.log('   - Tooltips and help text');
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'generate';
  
  console.log('🌐 Pseudolocalization Tool\n');
  
  switch (command) {
    case 'generate':
      const sourceLocale = args[1] || 'en-US';
      generatePseudolocale(sourceLocale);
      generateTestReport();
      break;
      
    case 'validate':
      validatePseudolocale();
      break;
      
    case 'help':
      console.log('Usage:');
      console.log('  npm run pseudolocalize [command] [options]');
      console.log('');
      console.log('Commands:');
      console.log('  generate [locale]  Generate pseudolocale from source locale (default: en-US)');
      console.log('  validate          Validate existing pseudolocale');
      console.log('  help              Show this help message');
      break;
      
    default:
      console.log(`Unknown command: ${command}`);
      console.log('Run "npm run pseudolocalize help" for usage information.');
      process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  pseudolocalizeString,
  pseudolocalizeObject,
  generatePseudolocale,
  validatePseudolocale,
  PSEUDO_CONFIG,
};
