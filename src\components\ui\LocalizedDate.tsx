'use client';

import { useLocale } from 'next-intl';
import { type Locale } from '@/i18n';
import { 
  formatDate, 
  formatTime, 
  formatDateTime, 
  formatRelativeTime,
  getLocaleConfig 
} from '@/lib/intl-formatters';

interface LocalizedDateProps {
  date: Date | string | number;
  format?: 'short' | 'medium' | 'long' | 'full';
  type?: 'date' | 'time' | 'datetime' | 'relative';
  className?: string;
  options?: Intl.DateTimeFormatOptions;
  baseDate?: Date; // For relative time
}

/**
 * Component for displaying dates with proper locale formatting
 */
export function LocalizedDate({
  date,
  format = 'medium',
  type = 'date',
  className,
  options = {},
  baseDate,
}: LocalizedDateProps) {
  const locale = useLocale() as Locale;
  const dateObj = new Date(date);

  // Validate date
  if (isNaN(dateObj.getTime())) {
    return <span className={className}>Invalid Date</span>;
  }

  let formattedValue: string;

  // Define format options based on format prop
  const getFormatOptions = (): Intl.DateTimeFormatOptions => {
    const baseOptions: Record<string, Intl.DateTimeFormatOptions> = {
      short: {
        year: '2-digit',
        month: 'numeric',
        day: 'numeric',
      },
      medium: {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      },
      long: {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      },
      full: {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      },
    };

    return { ...baseOptions[format], ...options };
  };

  switch (type) {
    case 'time':
      formattedValue = formatTime(dateObj, locale, options);
      break;
    case 'datetime':
      formattedValue = formatDateTime(dateObj, locale, getFormatOptions());
      break;
    case 'relative':
      formattedValue = formatRelativeTime(dateObj, locale, baseDate);
      break;
    default:
      formattedValue = formatDate(dateObj, locale, getFormatOptions());
  }

  return (
    <span className={className} title={dateObj.toISOString()}>
      {formattedValue}
    </span>
  );
}

interface LocalizedDateInputProps {
  value: Date | string | null;
  onChange: (date: Date | null) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  min?: string;
  max?: string;
  required?: boolean;
}

/**
 * Date input component that respects locale formatting
 */
export function LocalizedDateInput({
  value,
  onChange,
  placeholder,
  className,
  disabled,
  min,
  max,
  required,
}: LocalizedDateInputProps) {
  const locale = useLocale() as Locale;
  const config = getLocaleConfig(locale);

  // Convert value to input format (YYYY-MM-DD)
  const inputValue = value ? new Date(value).toISOString().split('T')[0] : '';

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputDate = event.target.value;
    
    if (inputDate === '') {
      onChange(null);
      return;
    }

    const date = new Date(inputDate);
    if (!isNaN(date.getTime())) {
      onChange(date);
    }
  };

  // Get localized placeholder if not provided
  const localizedPlaceholder = placeholder || getLocalizedDatePlaceholder(locale);

  return (
    <input
      type="date"
      value={inputValue}
      onChange={handleChange}
      placeholder={localizedPlaceholder}
      className={className}
      disabled={disabled}
      min={min}
      max={max}
      required={required}
    />
  );
}

interface LocalizedTimeInputProps {
  value: Date | string | null;
  onChange: (date: Date | null) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  step?: number; // in seconds
}

/**
 * Time input component that respects locale formatting
 */
export function LocalizedTimeInput({
  value,
  onChange,
  placeholder,
  className,
  disabled,
  required,
  step,
}: LocalizedTimeInputProps) {
  const locale = useLocale() as Locale;
  const config = getLocaleConfig(locale);

  // Convert value to input format (HH:MM or HH:MM:SS)
  const inputValue = value ? new Date(value).toTimeString().slice(0, step ? 8 : 5) : '';

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputTime = event.target.value;
    
    if (inputTime === '') {
      onChange(null);
      return;
    }

    // Create a date with today's date and the selected time
    const today = new Date();
    const [hours, minutes, seconds = '0'] = inputTime.split(':');
    const date = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 
                         parseInt(hours), parseInt(minutes), parseInt(seconds));
    
    if (!isNaN(date.getTime())) {
      onChange(date);
    }
  };

  // Get localized placeholder if not provided
  const localizedPlaceholder = placeholder || getLocalizedTimePlaceholder(locale);

  return (
    <input
      type="time"
      value={inputValue}
      onChange={handleChange}
      placeholder={localizedPlaceholder}
      className={className}
      disabled={disabled}
      required={required}
      step={step}
    />
  );
}

interface LocalizedDateTimeInputProps {
  value: Date | string | null;
  onChange: (date: Date | null) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  min?: string;
  max?: string;
  required?: boolean;
  step?: number;
}

/**
 * DateTime input component that respects locale formatting
 */
export function LocalizedDateTimeInput({
  value,
  onChange,
  placeholder,
  className,
  disabled,
  min,
  max,
  required,
  step,
}: LocalizedDateTimeInputProps) {
  const locale = useLocale() as Locale;

  // Convert value to input format (YYYY-MM-DDTHH:MM)
  const inputValue = value 
    ? new Date(value).toISOString().slice(0, step ? 19 : 16)
    : '';

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputDateTime = event.target.value;
    
    if (inputDateTime === '') {
      onChange(null);
      return;
    }

    const date = new Date(inputDateTime);
    if (!isNaN(date.getTime())) {
      onChange(date);
    }
  };

  // Get localized placeholder if not provided
  const localizedPlaceholder = placeholder || getLocalizedDateTimePlaceholder(locale);

  return (
    <input
      type="datetime-local"
      value={inputValue}
      onChange={handleChange}
      placeholder={localizedPlaceholder}
      className={className}
      disabled={disabled}
      min={min}
      max={max}
      required={required}
      step={step}
    />
  );
}

// Helper functions for localized placeholders
function getLocalizedDatePlaceholder(locale: Locale): string {
  const config = getLocaleConfig(locale);
  return config.dateFormat.toLowerCase();
}

function getLocalizedTimePlaceholder(locale: Locale): string {
  const config = getLocaleConfig(locale);
  return config.hour12 ? 'hh:mm am/pm' : 'hh:mm';
}

function getLocalizedDateTimePlaceholder(locale: Locale): string {
  const datePlaceholder = getLocalizedDatePlaceholder(locale);
  const timePlaceholder = getLocalizedTimePlaceholder(locale);
  return `${datePlaceholder} ${timePlaceholder}`;
}

interface DateRangePickerProps {
  startDate: Date | null;
  endDate: Date | null;
  onStartDateChange: (date: Date | null) => void;
  onEndDateChange: (date: Date | null) => void;
  className?: string;
  disabled?: boolean;
  required?: boolean;
}

/**
 * Date range picker component with locale support
 */
export function LocalizedDateRangePicker({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  className,
  disabled,
  required,
}: DateRangePickerProps) {
  const locale = useLocale() as Locale;

  // Ensure end date is not before start date
  const handleStartDateChange = (date: Date | null) => {
    onStartDateChange(date);
    
    if (date && endDate && date > endDate) {
      onEndDateChange(date);
    }
  };

  const handleEndDateChange = (date: Date | null) => {
    if (date && startDate && date < startDate) {
      onStartDateChange(date);
    }
    onEndDateChange(date);
  };

  const minEndDate = startDate ? startDate.toISOString().split('T')[0] : undefined;

  return (
    <div className={`flex space-x-4 ${className}`}>
      <div className="flex-1">
        <LocalizedDateInput
          value={startDate}
          onChange={handleStartDateChange}
          disabled={disabled}
          required={required}
          placeholder={locale === 'es-AR' ? 'Fecha desde' : 'From date'}
        />
      </div>
      <div className="flex-1">
        <LocalizedDateInput
          value={endDate}
          onChange={handleEndDateChange}
          disabled={disabled}
          required={required}
          min={minEndDate}
          placeholder={locale === 'es-AR' ? 'Fecha hasta' : 'To date'}
        />
      </div>
    </div>
  );
}
