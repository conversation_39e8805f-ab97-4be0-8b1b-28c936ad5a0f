'use client';

import { useLocale, useTranslations } from 'next-intl';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  TooltipProps,
} from 'recharts';

import { type Locale } from '@/i18n';
import { formatCurrency, formatNumber, formatDate } from '@/lib/intl-formatters';

// Chart color palette
const CHART_COLORS = [
  '#3B82F6', // blue-500
  '#10B981', // emerald-500
  '#F59E0B', // amber-500
  '#EF4444', // red-500
  '#8B5CF6', // violet-500
  '#06B6D4', // cyan-500
  '#84CC16', // lime-500
  '#F97316', // orange-500
];

interface ChartData {
  [key: string]: any;
}

interface LocalizedTooltipProps extends TooltipProps<any, any> {
  locale: Locale;
  formatters?: Record<string, (value: any) => string>;
}

function LocalizedTooltip({ 
  active, 
  payload, 
  label, 
  locale,
  formatters = {} 
}: LocalizedTooltipProps) {
  if (!active || !payload || !payload.length) {
    return null;
  }

  return (
    <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
      <p className="font-medium text-gray-900 mb-2">
        {typeof label === 'string' ? label : formatDate(label, locale)}
      </p>
      {payload.map((entry, index) => (
        <p key={index} className="text-sm" style={{ color: entry.color }}>
          <span className="font-medium">{entry.name}: </span>
          {formatters[entry.dataKey] 
            ? formatters[entry.dataKey](entry.value)
            : formatNumber(entry.value, locale)
          }
        </p>
      ))}
    </div>
  );
}

interface LocalizedBarChartProps {
  data: ChartData[];
  xKey: string;
  yKey: string;
  title?: string;
  height?: number;
  formatValue?: (value: any) => string;
  className?: string;
}

export function LocalizedBarChart({
  data,
  xKey,
  yKey,
  title,
  height = 300,
  formatValue,
  className,
}: LocalizedBarChartProps) {
  const locale = useLocale() as Locale;
  const t = useTranslations('charts');

  const defaultFormatter = (value: any) => {
    if (typeof value === 'number') {
      return formatNumber(value, locale);
    }
    return String(value);
  };

  const valueFormatter = formatValue || defaultFormatter;

  return (
    <div className={className}>
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {title}
        </h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
          <XAxis 
            dataKey={xKey} 
            stroke="#6B7280"
            fontSize={12}
          />
          <YAxis 
            stroke="#6B7280"
            fontSize={12}
            tickFormatter={valueFormatter}
          />
          <Tooltip 
            content={<LocalizedTooltip 
              locale={locale} 
              formatters={{ [yKey]: valueFormatter }}
            />}
          />
          <Bar 
            dataKey={yKey} 
            fill={CHART_COLORS[0]}
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}

interface LocalizedLineChartProps {
  data: ChartData[];
  xKey: string;
  lines: Array<{
    key: string;
    name: string;
    color?: string;
    formatter?: (value: any) => string;
  }>;
  title?: string;
  height?: number;
  className?: string;
}

export function LocalizedLineChart({
  data,
  xKey,
  lines,
  title,
  height = 300,
  className,
}: LocalizedLineChartProps) {
  const locale = useLocale() as Locale;
  const t = useTranslations('charts');

  const formatters = lines.reduce((acc, line) => {
    acc[line.key] = line.formatter || ((value: any) => formatNumber(value, locale));
    return acc;
  }, {} as Record<string, (value: any) => string>);

  return (
    <div className={className}>
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {title}
        </h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
          <XAxis 
            dataKey={xKey} 
            stroke="#6B7280"
            fontSize={12}
          />
          <YAxis 
            stroke="#6B7280"
            fontSize={12}
            tickFormatter={(value) => formatNumber(value, locale)}
          />
          <Tooltip 
            content={<LocalizedTooltip 
              locale={locale} 
              formatters={formatters}
            />}
          />
          <Legend />
          {lines.map((line, index) => (
            <Line
              key={line.key}
              type="monotone"
              dataKey={line.key}
              name={line.name}
              stroke={line.color || CHART_COLORS[index % CHART_COLORS.length]}
              strokeWidth={2}
              dot={{ r: 4 }}
              activeDot={{ r: 6 }}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

interface LocalizedPieChartProps {
  data: Array<{
    name: string;
    value: number;
    color?: string;
  }>;
  title?: string;
  height?: number;
  formatValue?: (value: any) => string;
  showPercentage?: boolean;
  className?: string;
}

export function LocalizedPieChart({
  data,
  title,
  height = 300,
  formatValue,
  showPercentage = true,
  className,
}: LocalizedPieChartProps) {
  const locale = useLocale() as Locale;
  const t = useTranslations('charts');

  const total = data.reduce((sum, item) => sum + item.value, 0);
  
  const defaultFormatter = (value: any) => {
    if (typeof value === 'number') {
      return formatNumber(value, locale);
    }
    return String(value);
  };

  const valueFormatter = formatValue || defaultFormatter;

  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (!showPercentage || percent < 0.05) return null; // Don't show labels for slices < 5%
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <div className={className}>
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {title}
        </h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderCustomLabel}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={entry.color || CHART_COLORS[index % CHART_COLORS.length]} 
              />
            ))}
          </Pie>
          <Tooltip 
            content={<LocalizedTooltipProps 
              locale={locale} 
              formatters={{ value: valueFormatter }}
            />}
          />
          <Legend 
            formatter={(value, entry) => (
              <span style={{ color: entry.color }}>
                {value}: {valueFormatter((entry.payload as any)?.value)}
              </span>
            )}
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}

// Specialized chart components for common business metrics

interface SalesChartProps {
  data: Array<{
    date: string;
    sales: number;
    target?: number;
  }>;
  title?: string;
  height?: number;
  className?: string;
}

export function SalesChart({ data, title, height = 300, className }: SalesChartProps) {
  const locale = useLocale() as Locale;
  const t = useTranslations('charts');

  const lines = [
    {
      key: 'sales',
      name: t('sales'),
      color: CHART_COLORS[0],
      formatter: (value: number) => formatCurrency(value, locale, 'ARS'),
    },
    ...(data.some(d => d.target) ? [{
      key: 'target',
      name: t('target'),
      color: CHART_COLORS[3],
      formatter: (value: number) => formatCurrency(value, locale, 'ARS'),
    }] : []),
  ];

  return (
    <LocalizedLineChart
      data={data}
      xKey="date"
      lines={lines}
      title={title || t('salesOverTime')}
      height={height}
      className={className}
    />
  );
}

interface TopProductsChartProps {
  data: Array<{
    name: string;
    sales: number;
  }>;
  title?: string;
  height?: number;
  className?: string;
}

export function TopProductsChart({ data, title, height = 300, className }: TopProductsChartProps) {
  const locale = useLocale() as Locale;
  const t = useTranslations('charts');

  return (
    <LocalizedBarChart
      data={data}
      xKey="name"
      yKey="sales"
      title={title || t('topProducts')}
      height={height}
      formatValue={(value) => formatCurrency(value, locale, 'ARS')}
      className={className}
    />
  );
}

interface PaymentMixChartProps {
  data: Array<{
    name: string;
    value: number;
  }>;
  title?: string;
  height?: number;
  className?: string;
}

export function PaymentMixChart({ data, title, height = 300, className }: PaymentMixChartProps) {
  const locale = useLocale() as Locale;
  const t = useTranslations('charts');

  return (
    <LocalizedPieChart
      data={data}
      title={title || t('paymentMix')}
      height={height}
      formatValue={(value) => formatCurrency(value, locale, 'ARS')}
      className={className}
    />
  );
}
