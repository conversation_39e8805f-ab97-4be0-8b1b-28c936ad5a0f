import { NextRequest, NextResponse } from 'next/server';
import { isValidLocale, type Locale } from '@/i18n';
import { headers } from 'next/headers';

/**
 * API endpoint to serve i18n bundles with proper caching
 * GET /api/i18n/[locale]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { locale: string } }
) {
  const { locale } = params;

  // Validate locale
  if (!isValidLocale(locale)) {
    return NextResponse.json(
      { error: 'Invalid locale', code: 'INVALID_LOCALE' },
      { status: 400 }
    );
  }

  try {
    // Import the locale file
    const messages = await import(`../../../../../locales/${locale}.json`);
    
    // Get current timestamp for ETag generation
    const lastModified = new Date().toUTCString();
    const etag = `"${Buffer.from(JSON.stringify(messages.default)).toString('base64').slice(0, 16)}"`;
    
    // Check if client has cached version
    const headersList = headers();
    const ifNoneMatch = headersList.get('if-none-match');
    const ifModifiedSince = headersList.get('if-modified-since');
    
    if (ifNoneMatch === etag || ifModifiedSince) {
      return new NextResponse(null, { 
        status: 304,
        headers: {
          'ETag': etag,
          'Last-Modified': lastModified,
          'Cache-Control': 'public, max-age=3600, must-revalidate',
        },
      });
    }

    // Return the messages with proper caching headers
    return NextResponse.json(messages.default, {
      headers: {
        'Content-Type': 'application/json',
        'ETag': etag,
        'Last-Modified': lastModified,
        'Cache-Control': 'public, max-age=3600, must-revalidate',
        'Vary': 'Accept-Language',
      },
    });
  } catch (error) {
    console.error(`Failed to load locale ${locale}:`, error);
    
    return NextResponse.json(
      { 
        error: 'Locale not found', 
        code: 'LOCALE_NOT_FOUND',
        locale 
      },
      { status: 404 }
    );
  }
}

/**
 * Handle preflight requests for CORS
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, If-None-Match, If-Modified-Since',
      'Access-Control-Max-Age': '86400',
    },
  });
}
