import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { isValidLocale, type Locale } from '@/i18n';
import { getLocalizedErrorMessage } from '@/lib/api-errors';
import { getUserPreferredLocale } from '@/lib/i18n-utils';

const prisma = new PrismaClient();

// Validation schema
const updateLocaleSchema = z.object({
  locale: z.string().refine(isValidLocale, {
    message: 'Invalid locale format',
  }),
});

/**
 * Update user's locale preference
 * PATCH /api/user/locale
 */
export async function PATCH(request: NextRequest) {
  try {
    // Get user session (you'll need to implement authentication)
    const session = await getServerSession();
    
    if (!session?.user?.id) {
      const locale = getUserPreferredLocale(
        null,
        null,
        request.headers.get('accept-language') || undefined
      );
      
      return NextResponse.json(
        { 
          error: getLocalizedErrorMessage('unauthorized', locale),
          code: 'UNAUTHORIZED' 
        },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validation = updateLocaleSchema.safeParse(body);
    
    if (!validation.success) {
      const locale = getUserPreferredLocale(
        null,
        null,
        request.headers.get('accept-language') || undefined
      );
      
      return NextResponse.json(
        {
          error: getLocalizedErrorMessage('validation', locale),
          code: 'VALIDATION_ERROR',
          details: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const { locale } = validation.data;

    // Update user's locale preference
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: { localePreference: locale as Locale },
      select: {
        id: true,
        localePreference: true,
        tenant: {
          select: {
            defaultLocale: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      locale: updatedUser.localePreference,
      message: getLocalizedErrorMessage('settingsSaved', locale as Locale),
    });
  } catch (error) {
    console.error('Failed to update user locale:', error);
    
    const locale = getUserPreferredLocale(
      null,
      null,
      request.headers.get('accept-language') || undefined
    );
    
    return NextResponse.json(
      {
        error: getLocalizedErrorMessage('serverError', locale),
        code: 'INTERNAL_SERVER_ERROR',
      },
      { status: 500 }
    );
  }
}

/**
 * Get user's current locale preference
 * GET /api/user/locale
 */
export async function GET(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession();
    
    if (!session?.user?.id) {
      // Return browser/default locale for unauthenticated users
      const browserLocale = getUserPreferredLocale(
        null,
        null,
        request.headers.get('accept-language') || undefined
      );
      
      return NextResponse.json({
        locale: browserLocale,
        source: 'browser',
      });
    }

    // Get user with tenant info
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        localePreference: true,
        tenant: {
          select: {
            defaultLocale: true,
          },
        },
      },
    });

    if (!user) {
      const locale = getUserPreferredLocale(
        null,
        null,
        request.headers.get('accept-language') || undefined
      );
      
      return NextResponse.json(
        {
          error: getLocalizedErrorMessage('notFound', locale),
          code: 'USER_NOT_FOUND',
        },
        { status: 404 }
      );
    }

    // Determine effective locale
    const effectiveLocale = getUserPreferredLocale(
      user.localePreference,
      user.tenant.defaultLocale,
      request.headers.get('accept-language') || undefined
    );

    return NextResponse.json({
      locale: effectiveLocale,
      userPreference: user.localePreference,
      tenantDefault: user.tenant.defaultLocale,
      source: user.localePreference ? 'user' : 'tenant',
    });
  } catch (error) {
    console.error('Failed to get user locale:', error);
    
    const locale = getUserPreferredLocale(
      null,
      null,
      request.headers.get('accept-language') || undefined
    );
    
    return NextResponse.json(
      {
        error: getLocalizedErrorMessage('serverError', locale),
        code: 'INTERNAL_SERVER_ERROR',
      },
      { status: 500 }
    );
  }
}
