{"name": "erp-argentina", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:i18n": "node scripts/check-hardcoded-strings.js", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "extract-strings": "node scripts/extract-strings.js", "pseudolocalize": "node scripts/pseudolocalize.js"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.2.0", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@prisma/client": "^5.6.0", "prisma": "^5.6.0", "next-intl": "^3.0.0", "@formatjs/intl": "^2.10.0", "intl-messageformat": "^10.5.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "recharts": "^2.8.0", "jspdf": "^2.5.0", "html2canvas": "^1.4.0", "papaparse": "^5.4.0", "@types/papaparse": "^5.3.0", "zod": "^3.22.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.0", "zustand": "^4.4.0", "swr": "^2.2.0", "axios": "^1.6.0"}, "devDependencies": {"eslint": "^8.52.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/user-event": "^14.5.0", "@playwright/test": "^1.40.0", "eslint-plugin-formatjs": "^4.11.0"}, "engines": {"node": ">=18.0.0"}}