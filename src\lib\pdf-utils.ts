/**
 * PDF Export Utilities with Localization Support
 * Handles PDF generation for reports, receipts, and dashboards
 */

import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { type Locale } from '@/i18n';
import { formatCurrency, formatNumber, formatDate } from '@/lib/intl-formatters';

// PDF Configuration
const PDF_CONFIG = {
  format: 'a4' as const,
  orientation: 'portrait' as const,
  unit: 'mm' as const,
  margins: {
    top: 20,
    right: 20,
    bottom: 20,
    left: 20,
  },
  fonts: {
    primary: 'helvetica',
    secondary: 'times',
  },
  colors: {
    primary: '#1F2937',
    secondary: '#6B7280',
    accent: '#3B82F6',
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
  },
};

interface PDFOptions {
  locale: Locale;
  title: string;
  subtitle?: string;
  filename?: string;
  orientation?: 'portrait' | 'landscape';
  includeTimestamp?: boolean;
  includePageNumbers?: boolean;
  watermark?: string;
}

interface PDFTableColumn {
  key: string;
  label: string;
  width: number; // percentage of total width
  align: 'left' | 'center' | 'right';
  type: 'string' | 'number' | 'currency' | 'date';
}

interface PDFTableData {
  columns: PDFTableColumn[];
  rows: any[];
}

/**
 * Create a new PDF document with localized settings
 */
export function createPDF(options: PDFOptions): jsPDF {
  const doc = new jsPDF({
    orientation: options.orientation || 'portrait',
    unit: PDF_CONFIG.unit,
    format: PDF_CONFIG.format,
  });

  // Set font
  doc.setFont(PDF_CONFIG.fonts.primary);
  
  return doc;
}

/**
 * Add header to PDF
 */
export function addPDFHeader(
  doc: jsPDF,
  options: PDFOptions,
  businessInfo?: {
    name: string;
    address?: string;
    phone?: string;
    email?: string;
  }
): number {
  const pageWidth = doc.internal.pageSize.getWidth();
  let yPosition = PDF_CONFIG.margins.top;

  // Business info (if provided)
  if (businessInfo) {
    doc.setFontSize(12);
    doc.setTextColor(PDF_CONFIG.colors.secondary);
    doc.text(businessInfo.name, PDF_CONFIG.margins.left, yPosition);
    yPosition += 6;

    if (businessInfo.address) {
      doc.setFontSize(10);
      doc.text(businessInfo.address, PDF_CONFIG.margins.left, yPosition);
      yPosition += 5;
    }

    if (businessInfo.phone || businessInfo.email) {
      const contact = [businessInfo.phone, businessInfo.email].filter(Boolean).join(' | ');
      doc.text(contact, PDF_CONFIG.margins.left, yPosition);
      yPosition += 10;
    }
  }

  // Title
  doc.setFontSize(18);
  doc.setTextColor(PDF_CONFIG.colors.primary);
  doc.text(options.title, PDF_CONFIG.margins.left, yPosition);
  yPosition += 8;

  // Subtitle
  if (options.subtitle) {
    doc.setFontSize(12);
    doc.setTextColor(PDF_CONFIG.colors.secondary);
    doc.text(options.subtitle, PDF_CONFIG.margins.left, yPosition);
    yPosition += 6;
  }

  // Timestamp
  if (options.includeTimestamp !== false) {
    const timestamp = formatDate(new Date(), options.locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
    
    doc.setFontSize(10);
    doc.setTextColor(PDF_CONFIG.colors.secondary);
    doc.text(timestamp, pageWidth - PDF_CONFIG.margins.right, yPosition, { align: 'right' });
    yPosition += 10;
  }

  // Add separator line
  doc.setDrawColor(PDF_CONFIG.colors.secondary);
  doc.setLineWidth(0.5);
  doc.line(
    PDF_CONFIG.margins.left,
    yPosition,
    pageWidth - PDF_CONFIG.margins.right,
    yPosition
  );
  yPosition += 10;

  return yPosition;
}

/**
 * Add footer to PDF
 */
export function addPDFFooter(
  doc: jsPDF,
  options: PDFOptions,
  pageNumber?: number,
  totalPages?: number
): void {
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const footerY = pageHeight - PDF_CONFIG.margins.bottom + 5;

  doc.setFontSize(8);
  doc.setTextColor(PDF_CONFIG.colors.secondary);

  // Page numbers
  if (options.includePageNumbers !== false && pageNumber && totalPages) {
    const pageText = options.locale === 'es-AR' 
      ? `Página ${pageNumber} de ${totalPages}`
      : `Page ${pageNumber} of ${totalPages}`;
    doc.text(pageText, pageWidth - PDF_CONFIG.margins.right, footerY, { align: 'right' });
  }

  // Watermark
  if (options.watermark) {
    doc.text(options.watermark, PDF_CONFIG.margins.left, footerY);
  }
}

/**
 * Add table to PDF
 */
export function addPDFTable(
  doc: jsPDF,
  tableData: PDFTableData,
  startY: number,
  options: PDFOptions
): number {
  const pageWidth = doc.internal.pageSize.getWidth();
  const tableWidth = pageWidth - PDF_CONFIG.margins.left - PDF_CONFIG.margins.right;
  let yPosition = startY;

  // Calculate column widths
  const columnWidths = tableData.columns.map(col => (tableWidth * col.width) / 100);

  // Table header
  doc.setFillColor(240, 240, 240);
  doc.rect(
    PDF_CONFIG.margins.left,
    yPosition,
    tableWidth,
    8,
    'F'
  );

  doc.setFontSize(10);
  doc.setTextColor(PDF_CONFIG.colors.primary);
  doc.setFont(PDF_CONFIG.fonts.primary, 'bold');

  let xPosition = PDF_CONFIG.margins.left;
  tableData.columns.forEach((column, index) => {
    doc.text(
      column.label,
      xPosition + 2,
      yPosition + 5,
      { align: column.align }
    );
    xPosition += columnWidths[index];
  });

  yPosition += 8;

  // Table rows
  doc.setFont(PDF_CONFIG.fonts.primary, 'normal');
  doc.setFontSize(9);

  tableData.rows.forEach((row, rowIndex) => {
    // Check if we need a new page
    if (yPosition > doc.internal.pageSize.getHeight() - 40) {
      doc.addPage();
      yPosition = addPDFHeader(doc, options);
    }

    // Alternate row colors
    if (rowIndex % 2 === 0) {
      doc.setFillColor(250, 250, 250);
      doc.rect(
        PDF_CONFIG.margins.left,
        yPosition,
        tableWidth,
        6,
        'F'
      );
    }

    xPosition = PDF_CONFIG.margins.left;
    tableData.columns.forEach((column, colIndex) => {
      const cellValue = formatTableCell(row[column.key], column, options.locale);
      doc.text(
        cellValue,
        xPosition + 2,
        yPosition + 4,
        { align: column.align }
      );
      xPosition += columnWidths[colIndex];
    });

    yPosition += 6;
  });

  return yPosition + 10;
}

/**
 * Format table cell value based on column type
 */
function formatTableCell(value: any, column: PDFTableColumn, locale: Locale): string {
  if (value === null || value === undefined) {
    return '';
  }

  switch (column.type) {
    case 'currency':
      return formatCurrency(value, locale, 'ARS');
    case 'number':
      return formatNumber(value, locale);
    case 'date':
      return formatDate(new Date(value), locale, { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      });
    default:
      return String(value);
  }
}

/**
 * Add KPI summary section to PDF
 */
export function addKPISummary(
  doc: jsPDF,
  kpis: Array<{
    label: string;
    value: number;
    unit: 'currency' | 'percentage' | 'number';
    trend?: 'up' | 'down' | 'neutral';
  }>,
  startY: number,
  options: PDFOptions
): number {
  let yPosition = startY;
  const pageWidth = doc.internal.pageSize.getWidth();
  const sectionWidth = pageWidth - PDF_CONFIG.margins.left - PDF_CONFIG.margins.right;

  // Section title
  doc.setFontSize(14);
  doc.setTextColor(PDF_CONFIG.colors.primary);
  doc.setFont(PDF_CONFIG.fonts.primary, 'bold');
  
  const summaryTitle = options.locale === 'es-AR' ? 'Resumen de KPIs' : 'KPI Summary';
  doc.text(summaryTitle, PDF_CONFIG.margins.left, yPosition);
  yPosition += 10;

  // KPI grid (2 columns)
  const kpiWidth = sectionWidth / 2;
  const kpiHeight = 15;

  kpis.forEach((kpi, index) => {
    const col = index % 2;
    const row = Math.floor(index / 2);
    
    const x = PDF_CONFIG.margins.left + (col * kpiWidth);
    const y = yPosition + (row * kpiHeight);

    // KPI box
    doc.setFillColor(248, 250, 252);
    doc.rect(x, y, kpiWidth - 5, kpiHeight - 2, 'F');

    // KPI label
    doc.setFontSize(9);
    doc.setTextColor(PDF_CONFIG.colors.secondary);
    doc.setFont(PDF_CONFIG.fonts.primary, 'normal');
    doc.text(kpi.label, x + 2, y + 4);

    // KPI value
    doc.setFontSize(12);
    doc.setTextColor(PDF_CONFIG.colors.primary);
    doc.setFont(PDF_CONFIG.fonts.primary, 'bold');
    
    let formattedValue: string;
    switch (kpi.unit) {
      case 'currency':
        formattedValue = formatCurrency(kpi.value, options.locale, 'ARS');
        break;
      case 'percentage':
        formattedValue = `${(kpi.value * 100).toFixed(1)}%`;
        break;
      default:
        formattedValue = formatNumber(kpi.value, options.locale);
    }
    
    doc.text(formattedValue, x + 2, y + 10);

    // Trend indicator
    if (kpi.trend) {
      const trendColor = kpi.trend === 'up' ? PDF_CONFIG.colors.success : 
                        kpi.trend === 'down' ? PDF_CONFIG.colors.error : 
                        PDF_CONFIG.colors.secondary;
      
      doc.setTextColor(trendColor);
      doc.setFontSize(8);
      const trendSymbol = kpi.trend === 'up' ? '↑' : kpi.trend === 'down' ? '↓' : '→';
      doc.text(trendSymbol, x + kpiWidth - 15, y + 10);
    }
  });

  return yPosition + Math.ceil(kpis.length / 2) * kpiHeight + 10;
}

/**
 * Export HTML element to PDF
 */
export async function exportElementToPDF(
  elementId: string,
  options: PDFOptions
): Promise<void> {
  try {
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error(`Element with ID "${elementId}" not found`);
    }

    // Create canvas from HTML element
    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
    });

    // Create PDF
    const doc = createPDF(options);
    const imgData = canvas.toDataURL('image/png');
    
    // Calculate dimensions
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const imgWidth = pageWidth - (PDF_CONFIG.margins.left + PDF_CONFIG.margins.right);
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    // Add header
    const startY = addPDFHeader(doc, options);

    // Add image
    if (imgHeight <= pageHeight - startY - PDF_CONFIG.margins.bottom) {
      // Fits on one page
      doc.addImage(imgData, 'PNG', PDF_CONFIG.margins.left, startY, imgWidth, imgHeight);
    } else {
      // Split across multiple pages
      let remainingHeight = imgHeight;
      let sourceY = 0;
      let currentY = startY;

      while (remainingHeight > 0) {
        const availableHeight = pageHeight - currentY - PDF_CONFIG.margins.bottom;
        const sliceHeight = Math.min(remainingHeight, availableHeight);
        
        // Calculate source dimensions for this slice
        const sourceHeight = (sliceHeight * canvas.height) / imgHeight;
        
        // Create canvas for this slice
        const sliceCanvas = document.createElement('canvas');
        sliceCanvas.width = canvas.width;
        sliceCanvas.height = sourceHeight;
        
        const sliceCtx = sliceCanvas.getContext('2d');
        if (sliceCtx) {
          sliceCtx.drawImage(
            canvas,
            0, sourceY, canvas.width, sourceHeight,
            0, 0, canvas.width, sourceHeight
          );
          
          const sliceImgData = sliceCanvas.toDataURL('image/png');
          doc.addImage(sliceImgData, 'PNG', PDF_CONFIG.margins.left, currentY, imgWidth, sliceHeight);
        }

        remainingHeight -= sliceHeight;
        sourceY += sourceHeight;

        if (remainingHeight > 0) {
          doc.addPage();
          currentY = addPDFHeader(doc, options);
        }
      }
    }

    // Add footer
    addPDFFooter(doc, options, 1, 1);

    // Download PDF
    const filename = options.filename || `export_${Date.now()}.pdf`;
    doc.save(filename);

  } catch (error) {
    console.error('PDF export failed:', error);
    throw new Error('Failed to export PDF');
  }
}

/**
 * Generate filename for PDF export
 */
export function generatePDFFilename(
  baseName: string,
  locale: Locale,
  includeTimestamp: boolean = true
): string {
  const timestamp = includeTimestamp 
    ? new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')
    : '';
  
  const localeSuffix = locale === 'es-AR' ? 'es' : 'en';
  
  const parts = [baseName, localeSuffix, timestamp].filter(Boolean);
  return `${parts.join('_')}.pdf`;
}
