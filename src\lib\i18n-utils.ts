/**
 * i18n Utility Functions
 * Helper functions for internationalization and localization
 */

import { type Locale, defaultLocale, getLocaleFromString } from '@/i18n';
import { i18nTelemetry } from './i18n-telemetry';

/**
 * Get user's preferred locale from various sources
 */
export function getUserPreferredLocale(
  userLocale?: string | null,
  tenantLocale?: string | null,
  browserLocale?: string
): Locale {
  // Priority: user preference > tenant default > browser > system default
  
  if (userLocale) {
    return getLocaleFromString(userLocale);
  }
  
  if (tenantLocale) {
    return getLocaleFromString(tenantLocale);
  }
  
  if (browserLocale) {
    return getLocaleFromString(browserLocale);
  }
  
  return defaultLocale;
}

/**
 * Get browser locale from Accept-Language header or navigator.language
 */
export function getBrowserLocale(acceptLanguage?: string): string | undefined {
  if (typeof window !== 'undefined') {
    // Client-side: use navigator.language
    return navigator.language || navigator.languages?.[0];
  }
  
  if (acceptLanguage) {
    // Server-side: parse Accept-Language header
    const languages = acceptLanguage
      .split(',')
      .map(lang => lang.trim().split(';')[0])
      .filter(Boolean);
    
    return languages[0];
  }
  
  return undefined;
}

/**
 * Safe translation function with fallback
 */
export function safeTranslate(
  messages: Record<string, any>,
  key: string,
  locale: Locale,
  fallbackMessages?: Record<string, any>
): string {
  // Try to get the translation
  const translation = getNestedValue(messages, key);
  
  if (translation && typeof translation === 'string') {
    return translation;
  }
  
  // Log missing key
  i18nTelemetry.logMissingKey(locale, key);
  
  // Try fallback messages
  if (fallbackMessages && locale !== defaultLocale) {
    const fallbackTranslation = getNestedValue(fallbackMessages, key);
    
    if (fallbackTranslation && typeof fallbackTranslation === 'string') {
      i18nTelemetry.logFallbackUsed(locale, defaultLocale, key);
      return fallbackTranslation;
    }
  }
  
  // Return the key as last resort
  return key;
}

/**
 * Get nested value from object using dot notation
 */
function getNestedValue(obj: Record<string, any>, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && typeof current === 'object' ? current[key] : undefined;
  }, obj);
}

/**
 * Format currency with proper locale formatting
 */
export function formatCurrency(
  amount: number,
  locale: Locale,
  currency: 'ARS' | 'USD' = 'ARS',
  options?: Intl.NumberFormatOptions
): string {
  const formatOptions: Intl.NumberFormatOptions = {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    ...options,
  };
  
  try {
    return new Intl.NumberFormat(locale, formatOptions).format(amount);
  } catch (error) {
    console.warn(`Failed to format currency for locale ${locale}:`, error);
    // Fallback formatting
    const symbol = currency === 'ARS' ? '$' : 'USD ';
    const separator = locale === 'es-AR' ? ',' : '.';
    const thousands = locale === 'es-AR' ? '.' : ',';
    
    const formatted = amount.toFixed(2).replace('.', separator);
    const withThousands = formatted.replace(/\B(?=(\d{3})+(?!\d))/g, thousands);
    
    return `${symbol}${withThousands}`;
  }
}

/**
 * Format number with proper locale formatting
 */
export function formatNumber(
  number: number,
  locale: Locale,
  options?: Intl.NumberFormatOptions
): string {
  try {
    return new Intl.NumberFormat(locale, options).format(number);
  } catch (error) {
    console.warn(`Failed to format number for locale ${locale}:`, error);
    return number.toString();
  }
}

/**
 * Format date with proper locale formatting
 */
export function formatDate(
  date: Date | string,
  locale: Locale,
  options?: Intl.DateTimeFormatOptions
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    ...options,
  };
  
  try {
    return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
  } catch (error) {
    console.warn(`Failed to format date for locale ${locale}:`, error);
    return dateObj.toLocaleDateString();
  }
}

/**
 * Format time with proper locale formatting
 */
export function formatTime(
  date: Date | string,
  locale: Locale,
  options?: Intl.DateTimeFormatOptions
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: '2-digit',
    hour12: locale === 'en-US', // 12h for US, 24h for Argentina
    ...options,
  };
  
  try {
    return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
  } catch (error) {
    console.warn(`Failed to format time for locale ${locale}:`, error);
    return dateObj.toLocaleTimeString();
  }
}

/**
 * Get relative time formatting (e.g., "2 hours ago")
 */
export function formatRelativeTime(
  date: Date | string,
  locale: Locale,
  baseDate: Date = new Date()
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const diffMs = baseDate.getTime() - dateObj.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  try {
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
    
    if (diffDays > 0) {
      return rtf.format(-diffDays, 'day');
    } else if (diffHours > 0) {
      return rtf.format(-diffHours, 'hour');
    } else if (diffMinutes > 0) {
      return rtf.format(-diffMinutes, 'minute');
    } else {
      return rtf.format(-diffSeconds, 'second');
    }
  } catch (error) {
    console.warn(`Failed to format relative time for locale ${locale}:`, error);
    return formatDate(dateObj, locale);
  }
}

/**
 * Validate and normalize locale string
 */
export function normalizeLocale(locale: string): Locale {
  return getLocaleFromString(locale);
}

/**
 * Get locale-specific decimal and thousands separators
 */
export function getLocaleSeparators(locale: Locale): { decimal: string; thousands: string } {
  try {
    const formatted = new Intl.NumberFormat(locale).format(1234.5);
    const decimal = formatted.includes(',') && formatted.includes('.') 
      ? (formatted.indexOf(',') > formatted.indexOf('.') ? ',' : '.')
      : (locale === 'es-AR' ? ',' : '.');
    const thousands = decimal === ',' ? '.' : ',';
    
    return { decimal, thousands };
  } catch (error) {
    // Fallback
    return locale === 'es-AR' 
      ? { decimal: ',', thousands: '.' }
      : { decimal: '.', thousands: ',' };
  }
}
