# ERP Argentina - Multi-Language Business Management System

A modern, mobile-first ERP system designed for small and medium businesses in Argentina, with full internationalization support for Spanish (es-AR) and English (en-US).

## 🌐 Internationalization Features

### Supported Locales
- **es-AR** (Spanish - Argentina) - Default locale
- **en-US** (English - United States)

### Key i18n Features
- ✅ Per-tenant default locale configuration
- ✅ Per-user locale preference override
- ✅ Instant language switching without page reload
- ✅ ICU MessageFormat with stable translation keys
- ✅ Locale-aware number, currency, and date formatting
- ✅ Fallback to English with telemetry logging
- ✅ Pluralization and gender support
- ✅ Localized PDF exports and CSV formats
- ✅ WhatsApp/email templates in recipient's language
- ✅ API error messages in user's locale
- ✅ Dashboard KPIs and charts fully localized

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- PostgreSQL 12+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd erp-argentina
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your database credentials
   ```

4. **Set up the database**
   ```bash
   npm run db:generate
   npm run db:push
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🧪 Testing

### Unit Tests
```bash
npm test
npm run test:watch
```

### E2E Tests
```bash
npm run test:e2e
```

### i18n Testing
```bash
# Check for hardcoded strings
npm run lint:i18n

# Generate pseudolocale for testing
npm run pseudolocalize

# Validate pseudolocale
npm run pseudolocalize validate
```

## 🌍 Localization Workflow

### Adding New Translations

1. **Add keys to locale files**
   ```json
   // locales/es-AR.json
   {
     "products": {
       "newFeature": "Nueva Funcionalidad"
     }
   }
   
   // locales/en-US.json
   {
     "products": {
       "newFeature": "New Feature"
     }
   }
   ```

2. **Use in components**
   ```tsx
   import { useTranslations } from 'next-intl';
   
   function ProductComponent() {
     const t = useTranslations('products');
     return <h1>{t('newFeature')}</h1>;
   }
   ```

### Translation Key Naming Convention
- Use dot notation: `section.subsection.key`
- Keep keys stable and descriptive
- Examples:
  - `common.save` - Common UI elements
  - `products.addProduct` - Feature-specific actions
  - `errors.validation` - Error messages
  - `kpi.salesTotal` - Dashboard metrics

### Formatting Guidelines

#### Currency
```tsx
import { LocalizedCurrency } from '@/components/ui/LocalizedNumber';

<LocalizedCurrency 
  value={1234.56} 
  showUSD={true} 
  exchangeRate={350} 
/>
// es-AR: $ 1.234,56 (≈ USD 3.53)
// en-US: $1,234.56 (≈ USD 3.53)
```

#### Numbers
```tsx
import { LocalizedNumber } from '@/components/ui/LocalizedNumber';

<LocalizedNumber value={1234.56} type="number" />
// es-AR: 1.234,56
// en-US: 1,234.56
```

#### Dates
```tsx
import { LocalizedDate } from '@/components/ui/LocalizedDate';

<LocalizedDate date={new Date()} type="date" format="medium" />
// es-AR: 25 dic 2023
// en-US: Dec 25, 2023
```

## 📊 CSV Export Formats

The system supports locale-specific CSV export formats:

- **Standard CSV (,)** - Uses comma delimiter, dot decimal separator
- **Excel Argentina (;)** - Uses semicolon delimiter, comma decimal separator

## 🔧 Development Tools

### Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:i18n` - Check for hardcoded strings
- `npm run type-check` - Run TypeScript checks
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run extract-strings` - Extract translatable strings
- `npm run pseudolocalize` - Generate pseudolocale for testing

### CI/CD Integration
The project includes CI checks for:
- Hardcoded string detection
- Missing translation keys
- Type safety
- Unit and E2E tests

## 🏗️ Architecture

### Tech Stack
- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Headless UI
- **i18n**: next-intl with ICU MessageFormat
- **Database**: PostgreSQL with Prisma ORM
- **Testing**: Jest, Playwright, Testing Library
- **Charts**: Recharts
- **PDF**: jsPDF
- **CSV**: PapaParse

### Project Structure
```
src/
├── app/                 # Next.js app router
├── components/          # React components
│   └── ui/             # Reusable UI components
├── lib/                # Utility libraries
│   ├── intl-formatters.ts
│   ├── i18n-utils.ts
│   └── api-errors.ts
├── hooks/              # Custom React hooks
├── types/              # TypeScript type definitions
└── __tests__/          # Unit tests

locales/                # Translation files
├── es-AR.json
├── en-US.json
└── en-XA.json         # Pseudolocale for testing

scripts/                # Development scripts
├── check-hardcoded-strings.js
└── pseudolocalize.js

tests/
└── e2e/               # End-to-end tests
```

## 🎯 Business Features

### Core Modules
- **Products & Inventory** - Product catalog, variants, stock management
- **Sales** - Non-fiscal sales, receipts, customer management
- **Purchases** - Purchase orders, supplier management, receiving
- **Cash Management** - Daily cash closing, expense tracking
- **Reports & Analytics** - KPIs, charts, PDF/CSV exports

### User Roles
- **Owner** - Full access, approvals, financial overview
- **Manager** - Product management, pricing, reports
- **Clerk/Cashier** - Sales, basic inventory operations

## 🌟 Key Features

### Mobile-First Design
- Responsive design optimized for tablets and phones
- Touch-friendly interface
- Offline capability (planned)

### Argentina-Specific
- ARS currency with USD reference
- Local date/time formats
- Business practices alignment
- No AFIP integration (MVP scope)

### Performance
- Server-side rendering
- Optimized bundle sizes
- Efficient i18n loading
- Database query optimization

## 🤝 Contributing

### Adding New Locales
1. Create new locale file in `locales/`
2. Add locale to `src/i18n.ts`
3. Update language switcher options
4. Add locale-specific formatting rules
5. Test with pseudolocalization

### Translation Guidelines
- Use clear, concise language
- Maintain consistent terminology
- Consider cultural context
- Test with longer text (pseudolocale)
- Verify number/date formatting

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation
- Review test files for examples
- Use pseudolocale for i18n testing
- Run `npm run lint:i18n` to catch issues

## 🔮 Roadmap

### Phase 1 (MVP) ✅
- Core i18n infrastructure
- Spanish/English support
- Basic business modules
- Mobile-responsive design

### Phase 2 (Planned)
- Offline capability
- Additional locales
- Advanced reporting
- API integrations

### Phase 3 (Future)
- AFIP integration
- Multi-currency support
- Advanced analytics
- Mobile apps
