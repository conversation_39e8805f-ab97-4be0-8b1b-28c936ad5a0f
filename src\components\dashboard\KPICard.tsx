'use client';

import { useLocale, useTranslations } from 'next-intl';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/solid';
import clsx from 'clsx';

import { type Locale } from '@/i18n';
import { LocalizedNumber, LocalizedCurrency } from '@/components/ui/LocalizedNumber';
import { formatPercentage, formatCompactNumber } from '@/lib/intl-formatters';

export interface KPIData {
  id: string;
  value: number;
  previousValue?: number;
  target?: number;
  unit: 'currency' | 'percentage' | 'number' | 'count';
  trend?: 'up' | 'down' | 'neutral';
  period: 'today' | 'week' | 'month' | 'quarter' | 'year';
  isStale?: boolean;
}

interface KPICardProps {
  kpi: KPIData;
  className?: string;
  compact?: boolean;
  showTrend?: boolean;
  showTarget?: boolean;
}

export function KPICard({
  kpi,
  className,
  compact = false,
  showTrend = true,
  showTarget = false,
}: KPICardProps) {
  const t = useTranslations('kpi');
  const locale = useLocale() as Locale;

  // Calculate percentage change
  const percentageChange = kpi.previousValue && kpi.previousValue !== 0
    ? ((kpi.value - kpi.previousValue) / kpi.previousValue) * 100
    : 0;

  // Determine trend direction
  const trendDirection = percentageChange > 0 ? 'up' : percentageChange < 0 ? 'down' : 'neutral';
  const actualTrend = kpi.trend || trendDirection;

  // Format the main value
  const formatValue = (value: number) => {
    switch (kpi.unit) {
      case 'currency':
        return compact 
          ? formatCompactNumber(value, locale)
          : <LocalizedCurrency value={value} />;
      case 'percentage':
        return formatPercentage(value / 100, locale);
      case 'count':
        return <LocalizedNumber value={value} type="number" maximumFractionDigits={0} />;
      default:
        return <LocalizedNumber value={value} type="number" />;
    }
  };

  // Get KPI title
  const getKPITitle = (id: string) => {
    // Map KPI IDs to translation keys
    const kpiTitleMap: Record<string, string> = {
      'sales_total': 'salesTotal',
      'sales_growth': 'salesGrowth',
      'profit_margin': 'profitMargin',
      'inventory_turnover': 'inventoryTurnover',
      'average_order_value': 'averageOrderValue',
      'customer_retention': 'customerRetention',
      'cost_of_goods_sold': 'costOfGoodsSold',
      'gross_profit': 'grossProfit',
      'net_profit': 'netProfit',
      'return_on_investment': 'returnOnInvestment',
      'cash_flow_positive': 'cashFlowPositive',
      'cash_flow_negative': 'cashFlowNegative',
      'stockout_rate': 'stockoutRate',
      'fill_rate': 'fillRate',
      'lead_time': 'leadTime',
      'order_accuracy': 'orderAccuracy',
      'customer_satisfaction': 'customerSatisfaction',
      'employee_productivity': 'employeeProductivity',
    };

    return t(kpiTitleMap[id] || id);
  };

  if (compact) {
    return (
      <div className={clsx(
        'bg-white rounded-lg border border-gray-200 p-4',
        kpi.isStale && 'border-yellow-300 bg-yellow-50',
        className
      )}>
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-600 truncate">
              {getKPITitle(kpi.id)}
            </p>
            <p className="text-lg font-semibold text-gray-900">
              {formatValue(kpi.value)}
            </p>
          </div>
          
          {showTrend && kpi.previousValue && (
            <div className={clsx(
              'flex items-center text-sm font-medium',
              actualTrend === 'up' && 'text-green-600',
              actualTrend === 'down' && 'text-red-600',
              actualTrend === 'neutral' && 'text-gray-500'
            )}>
              {actualTrend === 'up' && <ArrowUpIcon className="w-4 h-4 mr-1" />}
              {actualTrend === 'down' && <ArrowDownIcon className="w-4 h-4 mr-1" />}
              {formatPercentage(Math.abs(percentageChange) / 100, locale)}
            </div>
          )}
        </div>
        
        {kpi.isStale && (
          <div className="mt-2 text-xs text-yellow-700">
            {t('dataStale')}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={clsx(
      'bg-white rounded-lg border border-gray-200 p-6 shadow-sm',
      kpi.isStale && 'border-yellow-300 bg-yellow-50',
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-600">
          {getKPITitle(kpi.id)}
        </h3>
        
        {kpi.isStale && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            {t('stale')}
          </span>
        )}
      </div>

      {/* Main Value */}
      <div className="mb-4">
        <div className="text-3xl font-bold text-gray-900">
          {formatValue(kpi.value)}
        </div>
      </div>

      {/* Trend and Target */}
      <div className="flex items-center justify-between">
        {showTrend && kpi.previousValue && (
          <div className={clsx(
            'flex items-center text-sm font-medium',
            actualTrend === 'up' && 'text-green-600',
            actualTrend === 'down' && 'text-red-600',
            actualTrend === 'neutral' && 'text-gray-500'
          )}>
            {actualTrend === 'up' && <ArrowUpIcon className="w-4 h-4 mr-1" />}
            {actualTrend === 'down' && <ArrowDownIcon className="w-4 h-4 mr-1" />}
            <span>
              {formatPercentage(Math.abs(percentageChange) / 100, locale)}
            </span>
            <span className="ml-1 text-gray-500">
              {t('fromPrevious')}
            </span>
          </div>
        )}

        {showTarget && kpi.target && (
          <div className="text-sm text-gray-500">
            {t('target')}: {formatValue(kpi.target)}
          </div>
        )}
      </div>

      {/* Progress bar for target */}
      {showTarget && kpi.target && (
        <div className="mt-4">
          <div className="flex justify-between text-xs text-gray-500 mb-1">
            <span>{t('progress')}</span>
            <span>
              {formatPercentage((kpi.value / kpi.target), locale)}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={clsx(
                'h-2 rounded-full transition-all duration-300',
                kpi.value >= kpi.target ? 'bg-green-500' : 'bg-blue-500'
              )}
              style={{
                width: `${Math.min((kpi.value / kpi.target) * 100, 100)}%`,
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}

interface KPIGridProps {
  kpis: KPIData[];
  className?: string;
  compact?: boolean;
  columns?: 2 | 3 | 4;
}

export function KPIGrid({ 
  kpis, 
  className, 
  compact = false, 
  columns = 3 
}: KPIGridProps) {
  const gridCols = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <div className={clsx(
      'grid gap-4',
      gridCols[columns],
      className
    )}>
      {kpis.map((kpi) => (
        <KPICard
          key={kpi.id}
          kpi={kpi}
          compact={compact}
          showTrend={true}
          showTarget={!!kpi.target}
        />
      ))}
    </div>
  );
}
