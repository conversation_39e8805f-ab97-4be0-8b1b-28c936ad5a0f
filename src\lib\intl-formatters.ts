/**
 * Intl API Formatters for Localization
 * Provides consistent formatting for dates, numbers, and currencies
 */

import { type Locale } from '@/i18n';

// Currency configuration
export const CURRENCIES = {
  ARS: {
    code: 'ARS',
    symbol: '$',
    name: 'Argentine Peso',
    decimals: 2,
  },
  USD: {
    code: 'USD',
    symbol: 'USD',
    name: 'US Dollar',
    decimals: 2,
  },
} as const;

export type CurrencyCode = keyof typeof CURRENCIES;

// Locale-specific configurations
const LOCALE_CONFIGS = {
  'es-AR': {
    currency: 'ARS' as CurrencyCode,
    timeZone: 'America/Argentina/Buenos_Aires',
    hour12: false, // 24-hour format
    firstDayOfWeek: 1, // Monday
    dateFormat: 'dd/MM/yyyy',
    shortDateFormat: 'dd/MM/yy',
    longDateFormat: 'dd \'de\' MMMM \'de\' yyyy',
  },
  'en-US': {
    currency: 'USD' as CurrencyCode,
    timeZone: 'America/New_York',
    hour12: true, // 12-hour format
    firstDayOfWeek: 0, // Sunday
    dateFormat: 'MM/dd/yyyy',
    shortDateFormat: 'MM/dd/yy',
    longDateFormat: 'MMMM dd, yyyy',
  },
} as const;

/**
 * Get locale configuration
 */
export function getLocaleConfig(locale: Locale) {
  return LOCALE_CONFIGS[locale];
}

/**
 * Format currency with proper locale formatting
 */
export function formatCurrency(
  amount: number,
  locale: Locale,
  currencyCode?: CurrencyCode,
  options?: Partial<Intl.NumberFormatOptions>
): string {
  const config = getLocaleConfig(locale);
  const currency = currencyCode || config.currency;
  
  const formatOptions: Intl.NumberFormatOptions = {
    style: 'currency',
    currency,
    minimumFractionDigits: CURRENCIES[currency].decimals,
    maximumFractionDigits: CURRENCIES[currency].decimals,
    ...options,
  };

  try {
    return new Intl.NumberFormat(locale, formatOptions).format(amount);
  } catch (error) {
    console.warn(`Failed to format currency for locale ${locale}:`, error);
    
    // Fallback formatting
    const symbol = CURRENCIES[currency].symbol;
    const decimals = CURRENCIES[currency].decimals;
    const separator = locale === 'es-AR' ? ',' : '.';
    const thousands = locale === 'es-AR' ? '.' : ',';
    
    const formatted = amount.toFixed(decimals).replace('.', separator);
    const withThousands = formatted.replace(/\B(?=(\d{3})+(?!\d))/g, thousands);
    
    return currency === 'ARS' ? `${symbol} ${withThousands}` : `${symbol} ${withThousands}`;
  }
}

/**
 * Format currency with dual display (ARS primary, USD secondary)
 */
export function formatDualCurrency(
  amountARS: number,
  exchangeRate: number,
  locale: Locale,
  options?: { showUSD?: boolean; compact?: boolean }
): string {
  const { showUSD = true, compact = false } = options || {};
  
  const primaryFormatted = formatCurrency(amountARS, locale, 'ARS');
  
  if (!showUSD || exchangeRate <= 0) {
    return primaryFormatted;
  }
  
  const amountUSD = amountARS / exchangeRate;
  const secondaryFormatted = formatCurrency(amountUSD, locale, 'USD', { 
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
  
  const approximateSymbol = locale === 'es-AR' ? '≈' : '≈';
  
  if (compact) {
    return `${primaryFormatted} (${approximateSymbol} ${secondaryFormatted})`;
  }
  
  return `${primaryFormatted}\n${approximateSymbol} ${secondaryFormatted}`;
}

/**
 * Format number with proper locale formatting
 */
export function formatNumber(
  number: number,
  locale: Locale,
  options?: Intl.NumberFormatOptions
): string {
  const defaultOptions: Intl.NumberFormatOptions = {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
    ...options,
  };

  try {
    return new Intl.NumberFormat(locale, defaultOptions).format(number);
  } catch (error) {
    console.warn(`Failed to format number for locale ${locale}:`, error);
    return number.toString();
  }
}

/**
 * Format percentage with proper locale formatting
 */
export function formatPercentage(
  value: number,
  locale: Locale,
  options?: Intl.NumberFormatOptions
): string {
  const defaultOptions: Intl.NumberFormatOptions = {
    style: 'percent',
    minimumFractionDigits: 1,
    maximumFractionDigits: 2,
    ...options,
  };

  try {
    return new Intl.NumberFormat(locale, defaultOptions).format(value);
  } catch (error) {
    console.warn(`Failed to format percentage for locale ${locale}:`, error);
    return `${(value * 100).toFixed(1)}%`;
  }
}

/**
 * Format compact numbers (e.g., 1.2K, 1.5M)
 */
export function formatCompactNumber(
  number: number,
  locale: Locale,
  options?: Intl.NumberFormatOptions
): string {
  const defaultOptions: Intl.NumberFormatOptions = {
    notation: 'compact',
    compactDisplay: 'short',
    maximumFractionDigits: 1,
    ...options,
  };

  try {
    return new Intl.NumberFormat(locale, defaultOptions).format(number);
  } catch (error) {
    console.warn(`Failed to format compact number for locale ${locale}:`, error);
    
    // Fallback compact formatting
    if (number >= 1000000) {
      return `${(number / 1000000).toFixed(1)}M`;
    } else if (number >= 1000) {
      return `${(number / 1000).toFixed(1)}K`;
    }
    return number.toString();
  }
}

/**
 * Format date with proper locale formatting
 */
export function formatDate(
  date: Date | string | number,
  locale: Locale,
  options?: Intl.DateTimeFormatOptions
): string {
  const dateObj = new Date(date);
  const config = getLocaleConfig(locale);
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    timeZone: config.timeZone,
    ...options,
  };

  try {
    return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
  } catch (error) {
    console.warn(`Failed to format date for locale ${locale}:`, error);
    return dateObj.toLocaleDateString(locale);
  }
}

/**
 * Format time with proper locale formatting
 */
export function formatTime(
  date: Date | string | number,
  locale: Locale,
  options?: Intl.DateTimeFormatOptions
): string {
  const dateObj = new Date(date);
  const config = getLocaleConfig(locale);
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: '2-digit',
    hour12: config.hour12,
    timeZone: config.timeZone,
    ...options,
  };

  try {
    return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
  } catch (error) {
    console.warn(`Failed to format time for locale ${locale}:`, error);
    return dateObj.toLocaleTimeString(locale);
  }
}

/**
 * Format date and time together
 */
export function formatDateTime(
  date: Date | string | number,
  locale: Locale,
  options?: Intl.DateTimeFormatOptions
): string {
  const dateObj = new Date(date);
  const config = getLocaleConfig(locale);
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: config.hour12,
    timeZone: config.timeZone,
    ...options,
  };

  try {
    return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
  } catch (error) {
    console.warn(`Failed to format datetime for locale ${locale}:`, error);
    return dateObj.toLocaleString(locale);
  }
}

/**
 * Format relative time (e.g., "2 hours ago", "in 3 days")
 */
export function formatRelativeTime(
  date: Date | string | number,
  locale: Locale,
  baseDate: Date = new Date()
): string {
  const dateObj = new Date(date);
  const diffMs = dateObj.getTime() - baseDate.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  const diffWeeks = Math.floor(diffDays / 7);
  const diffMonths = Math.floor(diffDays / 30);
  const diffYears = Math.floor(diffDays / 365);

  try {
    const rtf = new Intl.RelativeTimeFormat(locale, { 
      numeric: 'auto',
      style: 'long',
    });

    if (Math.abs(diffYears) >= 1) {
      return rtf.format(diffYears, 'year');
    } else if (Math.abs(diffMonths) >= 1) {
      return rtf.format(diffMonths, 'month');
    } else if (Math.abs(diffWeeks) >= 1) {
      return rtf.format(diffWeeks, 'week');
    } else if (Math.abs(diffDays) >= 1) {
      return rtf.format(diffDays, 'day');
    } else if (Math.abs(diffHours) >= 1) {
      return rtf.format(diffHours, 'hour');
    } else if (Math.abs(diffMinutes) >= 1) {
      return rtf.format(diffMinutes, 'minute');
    } else {
      return rtf.format(diffSeconds, 'second');
    }
  } catch (error) {
    console.warn(`Failed to format relative time for locale ${locale}:`, error);
    return formatDate(dateObj, locale);
  }
}

/**
 * Get locale-specific decimal and thousands separators
 */
export function getLocaleSeparators(locale: Locale): { 
  decimal: string; 
  thousands: string; 
} {
  try {
    // Use a number with both decimal and thousands to detect separators
    const formatted = new Intl.NumberFormat(locale).format(1234.5);
    
    // Find decimal separator
    const decimalMatch = formatted.match(/[.,]/g);
    const decimal = decimalMatch ? decimalMatch[decimalMatch.length - 1] : '.';
    
    // Thousands separator is the opposite of decimal
    const thousands = decimal === ',' ? '.' : ',';
    
    return { decimal, thousands };
  } catch (error) {
    console.warn(`Failed to detect separators for locale ${locale}:`, error);
    
    // Fallback based on locale
    return locale === 'es-AR' 
      ? { decimal: ',', thousands: '.' }
      : { decimal: '.', thousands: ',' };
  }
}

/**
 * Parse localized number string to number
 */
export function parseLocalizedNumber(
  value: string,
  locale: Locale
): number | null {
  if (!value || typeof value !== 'string') {
    return null;
  }

  const { decimal, thousands } = getLocaleSeparators(locale);
  
  // Remove thousands separators and replace decimal separator with dot
  const normalized = value
    .replace(new RegExp(`\\${thousands}`, 'g'), '')
    .replace(decimal, '.');
  
  const parsed = parseFloat(normalized);
  return isNaN(parsed) ? null : parsed;
}

/**
 * Format number for input fields (respecting locale)
 */
export function formatNumberForInput(
  number: number,
  locale: Locale,
  decimals: number = 2
): string {
  const { decimal } = getLocaleSeparators(locale);
  return number.toFixed(decimals).replace('.', decimal);
}
