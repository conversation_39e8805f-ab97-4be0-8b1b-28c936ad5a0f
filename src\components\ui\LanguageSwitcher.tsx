'use client';

import { useState, useTransition } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useLocale, useTranslations } from 'next-intl';
import { ChevronDownIcon, LanguageIcon } from '@heroicons/react/24/outline';
import { Menu, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import clsx from 'clsx';

import { type Locale, locales } from '@/i18n';

// Language options with flags and names
const LANGUAGE_OPTIONS = {
  'es-AR': {
    code: 'es-AR',
    name: 'Español (Argentina)',
    nativeName: 'Español',
    flag: '🇦🇷',
  },
  'en-US': {
    code: 'en-US',
    name: 'English (United States)',
    nativeName: 'English',
    flag: '🇺🇸',
  },
} as const;

interface LanguageSwitcherProps {
  variant?: 'dropdown' | 'compact' | 'icon-only';
  className?: string;
  onLocaleChange?: (locale: Locale) => void;
}

export function LanguageSwitcher({ 
  variant = 'dropdown',
  className,
  onLocaleChange,
}: LanguageSwitcherProps) {
  const t = useTranslations('settings');
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = useLocale() as Locale;
  const [isPending, startTransition] = useTransition();
  const [isChanging, setIsChanging] = useState(false);

  const currentLanguage = LANGUAGE_OPTIONS[currentLocale];

  const handleLocaleChange = async (newLocale: Locale) => {
    if (newLocale === currentLocale) return;

    setIsChanging(true);

    try {
      // Update user preference in the backend
      if (onLocaleChange) {
        await onLocaleChange(newLocale);
      } else {
        // Default API call to update user locale preference
        await fetch('/api/user/locale', {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ locale: newLocale }),
        });
      }

      // Navigate to the new locale
      startTransition(() => {
        // Replace the locale in the current path
        const segments = pathname.split('/');
        if (locales.includes(segments[1] as Locale)) {
          segments[1] = newLocale;
        } else {
          segments.unshift('', newLocale);
        }
        
        const newPath = segments.join('/');
        router.replace(newPath);
      });
    } catch (error) {
      console.error('Failed to change locale:', error);
      // You might want to show a toast notification here
    } finally {
      setIsChanging(false);
    }
  };

  if (variant === 'icon-only') {
    return (
      <Menu as="div" className={clsx("relative inline-block text-left", className)}>
        <Menu.Button
          className={clsx(
            "inline-flex items-center justify-center rounded-md p-2",
            "text-gray-400 hover:text-gray-500 hover:bg-gray-100",
            "focus:outline-none focus:ring-2 focus:ring-primary-500",
            "transition-colors duration-200",
            (isPending || isChanging) && "opacity-50 cursor-not-allowed"
          )}
          disabled={isPending || isChanging}
        >
          <LanguageIcon className="h-5 w-5" />
        </Menu.Button>

        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
            {locales.map((locale) => {
              const language = LANGUAGE_OPTIONS[locale];
              return (
                <Menu.Item key={locale}>
                  {({ active }) => (
                    <button
                      onClick={() => handleLocaleChange(locale)}
                      className={clsx(
                        "flex w-full items-center px-4 py-2 text-sm",
                        active ? "bg-gray-100 text-gray-900" : "text-gray-700",
                        locale === currentLocale && "bg-primary-50 text-primary-700"
                      )}
                    >
                      <span className="mr-3 text-lg">{language.flag}</span>
                      <span>{language.nativeName}</span>
                      {locale === currentLocale && (
                        <span className="ml-auto text-primary-600">✓</span>
                      )}
                    </button>
                  )}
                </Menu.Item>
              );
            })}
          </Menu.Items>
        </Transition>
      </Menu>
    );
  }

  if (variant === 'compact') {
    return (
      <Menu as="div" className={clsx("relative inline-block text-left", className)}>
        <Menu.Button
          className={clsx(
            "inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700",
            "hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
            "transition-colors duration-200",
            (isPending || isChanging) && "opacity-50 cursor-not-allowed"
          )}
          disabled={isPending || isChanging}
        >
          <span className="mr-2 text-base">{currentLanguage.flag}</span>
          <span className="hidden sm:inline">{currentLanguage.nativeName}</span>
          <ChevronDownIcon className="ml-2 h-4 w-4" />
        </Menu.Button>

        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
            {locales.map((locale) => {
              const language = LANGUAGE_OPTIONS[locale];
              return (
                <Menu.Item key={locale}>
                  {({ active }) => (
                    <button
                      onClick={() => handleLocaleChange(locale)}
                      className={clsx(
                        "flex w-full items-center px-4 py-2 text-sm",
                        active ? "bg-gray-100 text-gray-900" : "text-gray-700",
                        locale === currentLocale && "bg-primary-50 text-primary-700"
                      )}
                    >
                      <span className="mr-3 text-lg">{language.flag}</span>
                      <div className="flex flex-col items-start">
                        <span className="font-medium">{language.nativeName}</span>
                        <span className="text-xs text-gray-500">{language.name}</span>
                      </div>
                      {locale === currentLocale && (
                        <span className="ml-auto text-primary-600">✓</span>
                      )}
                    </button>
                  )}
                </Menu.Item>
              );
            })}
          </Menu.Items>
        </Transition>
      </Menu>
    );
  }

  // Default dropdown variant
  return (
    <div className={clsx("space-y-2", className)}>
      <label className="block text-sm font-medium text-gray-700">
        {t('language')}
      </label>
      <Menu as="div" className="relative">
        <Menu.Button
          className={clsx(
            "relative w-full cursor-default rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left",
            "focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500",
            "transition-colors duration-200",
            (isPending || isChanging) && "opacity-50 cursor-not-allowed"
          )}
          disabled={isPending || isChanging}
        >
          <span className="flex items-center">
            <span className="mr-3 text-lg">{currentLanguage.flag}</span>
            <span className="block truncate">{currentLanguage.name}</span>
          </span>
          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
            <ChevronDownIcon className="h-5 w-5 text-gray-400" />
          </span>
        </Menu.Button>

        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Menu.Items className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
            {locales.map((locale) => {
              const language = LANGUAGE_OPTIONS[locale];
              return (
                <Menu.Item key={locale}>
                  {({ active }) => (
                    <button
                      onClick={() => handleLocaleChange(locale)}
                      className={clsx(
                        "relative w-full cursor-default select-none py-2 pl-3 pr-9 text-left",
                        active ? "bg-primary-600 text-white" : "text-gray-900",
                        locale === currentLocale && !active && "bg-primary-50 text-primary-700"
                      )}
                    >
                      <div className="flex items-center">
                        <span className="mr-3 text-lg">{language.flag}</span>
                        <div className="flex flex-col">
                          <span className="block truncate font-medium">
                            {language.nativeName}
                          </span>
                          <span className={clsx(
                            "block truncate text-sm",
                            active ? "text-primary-200" : "text-gray-500"
                          )}>
                            {language.name}
                          </span>
                        </div>
                      </div>
                      {locale === currentLocale && (
                        <span className={clsx(
                          "absolute inset-y-0 right-0 flex items-center pr-4",
                          active ? "text-white" : "text-primary-600"
                        )}>
                          ✓
                        </span>
                      )}
                    </button>
                  )}
                </Menu.Item>
              );
            })}
          </Menu.Items>
        </Transition>
      </Menu>
      
      {(isPending || isChanging) && (
        <p className="text-sm text-gray-500">
          {t('settingsSaved')}...
        </p>
      )}
    </div>
  );
}
