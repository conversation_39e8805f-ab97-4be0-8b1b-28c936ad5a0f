/**
 * i18n Telemetry System
 * Tracks missing translation keys and other i18n-related events
 */

interface TelemetryEvent {
  type: 'i18n_missing_key' | 'i18n_fallback_used' | 'i18n_load_error';
  locale: string;
  key?: string;
  fallbackLocale?: string;
  timestamp: Date;
  userAgent?: string;
  url?: string;
}

class I18nTelemetry {
  private events: TelemetryEvent[] = [];
  private maxEvents = 1000; // Prevent memory leaks

  /**
   * Log a missing translation key
   */
  logMissingKey(locale: string, key: string, url?: string) {
    const event: TelemetryEvent = {
      type: 'i18n_missing_key',
      locale,
      key,
      timestamp: new Date(),
      url,
    };

    this.addEvent(event);
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[i18n] Missing key "${key}" for locale "${locale}"`);
    }
  }

  /**
   * Log when fallback locale is used
   */
  logFallbackUsed(locale: string, fallbackLocale: string, key?: string) {
    const event: TelemetryEvent = {
      type: 'i18n_fallback_used',
      locale,
      fallbackLocale,
      key,
      timestamp: new Date(),
    };

    this.addEvent(event);
    
    if (process.env.NODE_ENV === 'development') {
      console.info(`[i18n] Using fallback "${fallbackLocale}" for locale "${locale}"${key ? ` (key: ${key})` : ''}`);
    }
  }

  /**
   * Log i18n loading errors
   */
  logLoadError(locale: string, error: Error) {
    const event: TelemetryEvent = {
      type: 'i18n_load_error',
      locale,
      timestamp: new Date(),
    };

    this.addEvent(event);
    
    console.error(`[i18n] Failed to load locale "${locale}":`, error);
  }

  /**
   * Add event to the queue
   */
  private addEvent(event: TelemetryEvent) {
    this.events.push(event);
    
    // Trim events if we exceed max
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // In production, you might want to send this to an analytics service
    if (process.env.NODE_ENV === 'production') {
      this.sendToAnalytics(event);
    }
  }

  /**
   * Send telemetry data to analytics service
   */
  private async sendToAnalytics(event: TelemetryEvent) {
    try {
      // Example: send to your analytics service
      // await fetch('/api/analytics/i18n', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(event),
      // });
    } catch (error) {
      // Silently fail - don't break the app for telemetry
      console.debug('Failed to send i18n telemetry:', error);
    }
  }

  /**
   * Get recent events (for debugging)
   */
  getRecentEvents(limit = 50): TelemetryEvent[] {
    return this.events.slice(-limit);
  }

  /**
   * Get missing keys summary
   */
  getMissingKeysSummary(): Record<string, { count: number; keys: string[] }> {
    const summary: Record<string, { count: number; keys: string[] }> = {};
    
    this.events
      .filter(event => event.type === 'i18n_missing_key')
      .forEach(event => {
        if (!event.locale || !event.key) return;
        
        if (!summary[event.locale]) {
          summary[event.locale] = { count: 0, keys: [] };
        }
        
        summary[event.locale].count++;
        if (!summary[event.locale].keys.includes(event.key)) {
          summary[event.locale].keys.push(event.key);
        }
      });
    
    return summary;
  }

  /**
   * Clear all events
   */
  clear() {
    this.events = [];
  }
}

// Singleton instance
export const i18nTelemetry = new I18nTelemetry();

// Export types for external use
export type { TelemetryEvent };
