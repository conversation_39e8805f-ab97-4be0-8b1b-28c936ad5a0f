# Database
DATABASE_URL="postgresql://username:password@localhost:5432/erp_argentina"

# Next.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# App Configuration
NODE_ENV="development"
APP_URL="http://localhost:3000"

# i18n Configuration
DEFAULT_LOCALE="es-AR"
SUPPORTED_LOCALES="es-AR,en-US"

# Feature Flags
ENABLE_PSEUDOLOCALE="true"
ENABLE_I18N_TELEMETRY="true"

# External Services (Optional)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"

# WhatsApp API (Optional)
# WHATSAPP_API_URL="https://api.whatsapp.com"
# WHATSAPP_API_TOKEN="your-whatsapp-token"

# Analytics (Optional)
# GOOGLE_ANALYTICS_ID="GA-XXXXXXXXX"
# MIXPANEL_TOKEN="your-mixpanel-token"

# File Storage (Optional)
# AWS_ACCESS_KEY_ID="your-aws-access-key"
# AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
# AWS_REGION="us-east-1"
# AWS_S3_BUCKET="your-s3-bucket"

# Redis (Optional - for caching)
# REDIS_URL="redis://localhost:6379"

# Rate Limiting
RATE_LIMIT_MAX="100"
RATE_LIMIT_WINDOW="900000"

# Security
BCRYPT_ROUNDS="12"
JWT_EXPIRES_IN="7d"

# Development
DEBUG_I18N="false"
DEBUG_API="false"

# Currency & Exchange
DEFAULT_CURRENCY="ARS"
USD_EXCHANGE_RATE="350.00"
ENABLE_DUAL_CURRENCY="true"

# Business Configuration
BUSINESS_TIMEZONE="America/Argentina/Buenos_Aires"
FISCAL_YEAR_START="01-01"

# Dashboard & Reports
DASHBOARD_CACHE_TTL="300"
REPORT_MAX_ROWS="10000"
PDF_GENERATION_TIMEOUT="30000"

# CSV Export Configuration
CSV_DELIMITER_STANDARD=","
CSV_DELIMITER_EXCEL_AR=";"
CSV_DECIMAL_STANDARD="."
CSV_DECIMAL_EXCEL_AR=","
