#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to check for hardcoded strings in the codebase
 * This helps ensure all user-facing text is properly internationalized
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuration
const CONFIG = {
  // Directories to scan
  scanDirs: ['src/app', 'src/components', 'src/pages'],
  
  // File extensions to check
  extensions: ['tsx', 'ts', 'jsx', 'js'],
  
  // Patterns that indicate hardcoded strings
  patterns: [
    // JSX text content
    />\s*[A-Z][^<>{]*[a-z][^<>{}]*</g,
    
    // String literals in JSX attributes (placeholder, title, etc.)
    /(placeholder|title|alt|aria-label|label)\s*=\s*["'][^"'{}]*[A-Za-z]{3,}[^"'{}]*["']/g,
    
    // <PERSON>ert, confirm, prompt messages
    /(alert|confirm|prompt)\s*\(\s*["'][^"']*[A-Za-z]{3,}[^"']*["']/g,
    
    // Console messages (warnings and errors)
    /console\.(warn|error|info)\s*\(\s*["'][^"']*[A-Za-z]{3,}[^"']*["']/g,
    
    // Throw new Error messages
    /throw\s+new\s+Error\s*\(\s*["'][^"']*[A-Za-z]{3,}[^"']*["']/g,
  ],
  
  // Patterns to ignore (false positives)
  ignorePatterns: [
    // Technical strings
    /\b(className|onClick|onChange|onSubmit|useState|useEffect|console\.log)\b/,
    
    // URLs and technical identifiers
    /https?:\/\/|www\.|\.com|\.org|\.net/,
    
    // CSS classes and IDs
    /class(Name)?\s*=\s*["'][^"']*["']/,
    
    // Import/export statements
    /from\s+["'][^"']*["']|import\s+["'][^"']*["']/,
    
    // File extensions and paths
    /\.(js|ts|jsx|tsx|css|scss|json|png|jpg|svg)["']/,
    
    // Single words or very short strings
    /^.{1,2}$/,
    
    // Numbers and technical values
    /^\d+$|^[A-Z_]+$/,
    
    // Common technical terms
    /\b(API|URL|HTTP|JSON|CSS|HTML|DOM|UUID|ID|SKU)\b/i,
  ],
  
  // Files to exclude
  excludeFiles: [
    'node_modules/**',
    'dist/**',
    'build/**',
    '.next/**',
    '**/*.test.*',
    '**/*.spec.*',
    '**/test/**',
    '**/tests/**',
  ],
  
  // Allowed hardcoded strings (exceptions)
  allowedStrings: [
    // Common UI elements
    'OK', 'Cancel', 'Yes', 'No',
    
    // Technical constants
    'UTF-8', 'ISO-8859-1',
    
    // Common abbreviations
    'etc', 'e.g.', 'i.e.',
    
    // Single characters
    'x', 'y', 'z', '+', '-', '*', '/', '=',
  ],
};

/**
 * Check if a string should be ignored
 */
function shouldIgnoreString(str, filePath) {
  // Remove quotes and trim
  const cleanStr = str.replace(/^["']|["']$/g, '').trim();
  
  // Check if it's in allowed strings
  if (CONFIG.allowedStrings.includes(cleanStr)) {
    return true;
  }
  
  // Check ignore patterns
  for (const pattern of CONFIG.ignorePatterns) {
    if (pattern.test(str)) {
      return true;
    }
  }
  
  // Ignore very short strings
  if (cleanStr.length < 3) {
    return true;
  }
  
  // Ignore strings that are all uppercase (likely constants)
  if (cleanStr === cleanStr.toUpperCase() && cleanStr.length < 10) {
    return true;
  }
  
  // Ignore strings that look like variable names
  if (/^[a-z][a-zA-Z0-9]*$/.test(cleanStr)) {
    return true;
  }
  
  return false;
}

/**
 * Extract hardcoded strings from file content
 */
function extractHardcodedStrings(content, filePath) {
  const findings = [];
  const lines = content.split('\n');
  
  for (const pattern of CONFIG.patterns) {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const matchedText = match[0];
      
      if (shouldIgnoreString(matchedText, filePath)) {
        continue;
      }
      
      // Find line number
      const beforeMatch = content.substring(0, match.index);
      const lineNumber = beforeMatch.split('\n').length;
      const line = lines[lineNumber - 1];
      
      findings.push({
        text: matchedText,
        line: lineNumber,
        column: match.index - beforeMatch.lastIndexOf('\n'),
        context: line.trim(),
        pattern: pattern.source,
      });
    }
  }
  
  return findings;
}

/**
 * Scan a single file
 */
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const findings = extractHardcodedStrings(content, filePath);
    
    return {
      file: filePath,
      findings,
    };
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return {
      file: filePath,
      findings: [],
      error: error.message,
    };
  }
}

/**
 * Get all files to scan
 */
function getFilesToScan() {
  const patterns = CONFIG.scanDirs.flatMap(dir =>
    CONFIG.extensions.map(ext => `${dir}/**/*.${ext}`)
  );
  
  const files = [];
  
  for (const pattern of patterns) {
    const matches = glob.sync(pattern, {
      ignore: CONFIG.excludeFiles,
    });
    files.push(...matches);
  }
  
  return [...new Set(files)]; // Remove duplicates
}

/**
 * Main function
 */
function main() {
  console.log('🔍 Checking for hardcoded strings...\n');
  
  const files = getFilesToScan();
  console.log(`Scanning ${files.length} files...\n`);
  
  let totalFindings = 0;
  let filesWithIssues = 0;
  
  for (const file of files) {
    const result = scanFile(file);
    
    if (result.error) {
      console.error(`❌ Error in ${file}: ${result.error}`);
      continue;
    }
    
    if (result.findings.length > 0) {
      filesWithIssues++;
      totalFindings += result.findings.length;
      
      console.log(`📄 ${file}`);
      
      for (const finding of result.findings) {
        console.log(`  ⚠️  Line ${finding.line}:${finding.column} - ${finding.text}`);
        console.log(`     Context: ${finding.context}`);
        console.log('');
      }
    }
  }
  
  console.log('\n📊 Summary:');
  console.log(`Files scanned: ${files.length}`);
  console.log(`Files with issues: ${filesWithIssues}`);
  console.log(`Total hardcoded strings found: ${totalFindings}`);
  
  if (totalFindings > 0) {
    console.log('\n💡 Recommendations:');
    console.log('1. Move hardcoded strings to locale files (locales/es-AR.json, locales/en-US.json)');
    console.log('2. Use useTranslations() hook in components');
    console.log('3. Use getLocalizedErrorMessage() for API error messages');
    console.log('4. Add exceptions to CONFIG.allowedStrings if needed');
    
    // Exit with error code for CI
    process.exit(1);
  } else {
    console.log('\n✅ No hardcoded strings found!');
    process.exit(0);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  extractHardcodedStrings,
  shouldIgnoreString,
  CONFIG,
};
