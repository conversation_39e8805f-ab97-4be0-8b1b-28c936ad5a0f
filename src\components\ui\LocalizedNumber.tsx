'use client';

import { useLocale } from 'next-intl';
import { type Locale } from '@/i18n';
import { 
  formatNumber, 
  formatCurrency, 
  formatPercentage, 
  formatCompactNumber,
  type CurrencyCode 
} from '@/lib/intl-formatters';

interface LocalizedNumberProps {
  value: number;
  type?: 'number' | 'currency' | 'percentage' | 'compact';
  currency?: CurrencyCode;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  className?: string;
  options?: Intl.NumberFormatOptions;
}

/**
 * Component for displaying numbers with proper locale formatting
 */
export function LocalizedNumber({
  value,
  type = 'number',
  currency,
  minimumFractionDigits,
  maximumFractionDigits,
  className,
  options = {},
}: LocalizedNumberProps) {
  const locale = useLocale() as Locale;

  // Merge custom options with defaults
  const formatOptions: Intl.NumberFormatOptions = {
    ...options,
    ...(minimumFractionDigits !== undefined && { minimumFractionDigits }),
    ...(maximumFractionDigits !== undefined && { maximumFractionDigits }),
  };

  let formattedValue: string;

  switch (type) {
    case 'currency':
      formattedValue = formatCurrency(value, locale, currency, formatOptions);
      break;
    case 'percentage':
      formattedValue = formatPercentage(value, locale, formatOptions);
      break;
    case 'compact':
      formattedValue = formatCompactNumber(value, locale, formatOptions);
      break;
    default:
      formattedValue = formatNumber(value, locale, formatOptions);
  }

  return (
    <span className={className} title={value.toString()}>
      {formattedValue}
    </span>
  );
}

interface LocalizedCurrencyProps {
  value: number;
  currency?: CurrencyCode;
  showUSD?: boolean;
  exchangeRate?: number;
  compact?: boolean;
  className?: string;
  options?: Intl.NumberFormatOptions;
}

/**
 * Component for displaying currency with dual display support (ARS + USD)
 */
export function LocalizedCurrency({
  value,
  currency,
  showUSD = false,
  exchangeRate = 0,
  compact = false,
  className,
  options = {},
}: LocalizedCurrencyProps) {
  const locale = useLocale() as Locale;

  // Primary currency display
  const primaryFormatted = formatCurrency(value, locale, currency, options);

  // Secondary USD display if requested and exchange rate available
  const shouldShowUSD = showUSD && exchangeRate > 0 && currency !== 'USD';
  
  if (!shouldShowUSD) {
    return (
      <span className={className} title={value.toString()}>
        {primaryFormatted}
      </span>
    );
  }

  const usdValue = value / exchangeRate;
  const secondaryFormatted = formatCurrency(usdValue, locale, 'USD', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });

  const approximateSymbol = '≈';

  if (compact) {
    return (
      <span className={className} title={`${value} (≈ ${usdValue.toFixed(2)} USD)`}>
        {primaryFormatted} ({approximateSymbol} {secondaryFormatted})
      </span>
    );
  }

  return (
    <div className={className}>
      <div className="font-medium">{primaryFormatted}</div>
      <div className="text-sm text-gray-500">
        {approximateSymbol} {secondaryFormatted}
      </div>
    </div>
  );
}

interface LocalizedInputProps {
  value: number | string;
  onChange: (value: number | null) => void;
  type?: 'number' | 'currency';
  currency?: CurrencyCode;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  min?: number;
  max?: number;
  step?: number;
  decimals?: number;
}

/**
 * Input component that handles locale-specific number formatting
 */
export function LocalizedNumberInput({
  value,
  onChange,
  type = 'number',
  currency,
  placeholder,
  className,
  disabled,
  min,
  max,
  step,
  decimals = 2,
}: LocalizedInputProps) {
  const locale = useLocale() as Locale;

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;
    
    if (inputValue === '') {
      onChange(null);
      return;
    }

    // Parse the localized number
    const { decimal, thousands } = getLocaleSeparators(locale);
    const normalized = inputValue
      .replace(new RegExp(`\\${thousands}`, 'g'), '')
      .replace(decimal, '.');
    
    const parsed = parseFloat(normalized);
    
    if (!isNaN(parsed)) {
      onChange(parsed);
    }
  };

  const handleBlur = () => {
    // Format the value when the input loses focus
    if (typeof value === 'number' && !isNaN(value)) {
      const formatted = formatNumberForInput(value, locale, decimals);
      // You might want to update the display value here
    }
  };

  // Format the display value
  const displayValue = typeof value === 'number' && !isNaN(value)
    ? formatNumberForInput(value, locale, decimals)
    : value?.toString() || '';

  return (
    <div className="relative">
      {type === 'currency' && currency && (
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <span className="text-gray-500 sm:text-sm">
            {currency === 'ARS' ? '$' : 'USD'}
          </span>
        </div>
      )}
      <input
        type="text"
        value={displayValue}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder={placeholder}
        className={className}
        disabled={disabled}
        min={min}
        max={max}
        step={step}
        style={{
          paddingLeft: type === 'currency' && currency ? '2rem' : undefined,
        }}
      />
    </div>
  );
}

// Helper function to get locale separators (should be imported from intl-formatters)
function getLocaleSeparators(locale: Locale): { decimal: string; thousands: string } {
  try {
    const formatted = new Intl.NumberFormat(locale).format(1234.5);
    const decimalMatch = formatted.match(/[.,]/g);
    const decimal = decimalMatch ? decimalMatch[decimalMatch.length - 1] : '.';
    const thousands = decimal === ',' ? '.' : ',';
    return { decimal, thousands };
  } catch (error) {
    return locale === 'es-AR' 
      ? { decimal: ',', thousands: '.' }
      : { decimal: '.', thousands: ',' };
  }
}

function formatNumberForInput(number: number, locale: Locale, decimals: number = 2): string {
  const { decimal } = getLocaleSeparators(locale);
  return number.toFixed(decimals).replace('.', decimal);
}
