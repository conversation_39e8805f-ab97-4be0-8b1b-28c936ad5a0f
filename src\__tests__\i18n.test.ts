/**
 * i18n System Tests
 * Tests for internationalization functionality
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { 
  formatCurrency, 
  formatNumber, 
  formatDate, 
  formatTime,
  getLocaleSeparators,
  parseLocalizedNumber,
} from '@/lib/intl-formatters';
import { 
  getUserPreferredLocale,
  safeTranslate,
} from '@/lib/i18n-utils';
import { i18nTelemetry } from '@/lib/i18n-telemetry';
import { getLocalizedErrorMessage } from '@/lib/api-errors';

describe('Intl Formatters', () => {
  describe('formatCurrency', () => {
    it('should format ARS currency for es-AR locale', () => {
      const result = formatCurrency(1234.56, 'es-AR', 'ARS');
      expect(result).toMatch(/\$.*1\.234,56/); // Should use dot for thousands, comma for decimal
    });

    it('should format USD currency for en-US locale', () => {
      const result = formatCurrency(1234.56, 'en-US', 'USD');
      expect(result).toMatch(/\$1,234\.56/); // Should use comma for thousands, dot for decimal
    });

    it('should handle zero values', () => {
      const result = formatCurrency(0, 'es-AR', 'ARS');
      expect(result).toMatch(/\$.*0,00/);
    });

    it('should handle negative values', () => {
      const result = formatCurrency(-100, 'es-AR', 'ARS');
      expect(result).toContain('-');
      expect(result).toContain('100');
    });
  });

  describe('formatNumber', () => {
    it('should format numbers with locale-specific separators', () => {
      const esResult = formatNumber(1234.56, 'es-AR');
      const enResult = formatNumber(1234.56, 'en-US');
      
      expect(esResult).toMatch(/1\.234,56/);
      expect(enResult).toMatch(/1,234\.56/);
    });

    it('should respect custom decimal places', () => {
      const result = formatNumber(123.456789, 'en-US', { 
        minimumFractionDigits: 3,
        maximumFractionDigits: 3 
      });
      expect(result).toMatch(/123\.457/); // Should round to 3 decimals
    });
  });

  describe('formatDate', () => {
    const testDate = new Date('2023-12-25T15:30:00Z');

    it('should format dates for different locales', () => {
      const esResult = formatDate(testDate, 'es-AR');
      const enResult = formatDate(testDate, 'en-US');
      
      expect(esResult).toBeTruthy();
      expect(enResult).toBeTruthy();
      expect(esResult).not.toBe(enResult);
    });

    it('should handle string dates', () => {
      const result = formatDate('2023-12-25', 'en-US');
      expect(result).toBeTruthy();
    });

    it('should handle timestamp numbers', () => {
      const result = formatDate(testDate.getTime(), 'en-US');
      expect(result).toBeTruthy();
    });
  });

  describe('formatTime', () => {
    const testDate = new Date('2023-12-25T15:30:00Z');

    it('should format time with 24h for es-AR', () => {
      const result = formatTime(testDate, 'es-AR');
      expect(result).toMatch(/\d{1,2}:\d{2}/);
      expect(result).not.toMatch(/AM|PM/i);
    });

    it('should format time with 12h for en-US', () => {
      const result = formatTime(testDate, 'en-US');
      expect(result).toMatch(/\d{1,2}:\d{2}/);
      expect(result).toMatch(/AM|PM/i);
    });
  });

  describe('getLocaleSeparators', () => {
    it('should return correct separators for es-AR', () => {
      const separators = getLocaleSeparators('es-AR');
      expect(separators.decimal).toBe(',');
      expect(separators.thousands).toBe('.');
    });

    it('should return correct separators for en-US', () => {
      const separators = getLocaleSeparators('en-US');
      expect(separators.decimal).toBe('.');
      expect(separators.thousands).toBe(',');
    });
  });

  describe('parseLocalizedNumber', () => {
    it('should parse es-AR formatted numbers', () => {
      const result = parseLocalizedNumber('1.234,56', 'es-AR');
      expect(result).toBe(1234.56);
    });

    it('should parse en-US formatted numbers', () => {
      const result = parseLocalizedNumber('1,234.56', 'en-US');
      expect(result).toBe(1234.56);
    });

    it('should return null for invalid input', () => {
      expect(parseLocalizedNumber('invalid', 'en-US')).toBeNull();
      expect(parseLocalizedNumber('', 'en-US')).toBeNull();
      expect(parseLocalizedNumber(null as any, 'en-US')).toBeNull();
    });
  });
});

describe('i18n Utils', () => {
  describe('getUserPreferredLocale', () => {
    it('should prioritize user preference', () => {
      const result = getUserPreferredLocale('en-US', 'es-AR', 'fr-FR');
      expect(result).toBe('en-US');
    });

    it('should fall back to tenant locale', () => {
      const result = getUserPreferredLocale(null, 'es-AR', 'fr-FR');
      expect(result).toBe('es-AR');
    });

    it('should fall back to browser locale', () => {
      const result = getUserPreferredLocale(null, null, 'en-US');
      expect(result).toBe('en-US');
    });

    it('should fall back to default locale', () => {
      const result = getUserPreferredLocale(null, null, null);
      expect(result).toBe('es-AR'); // Default locale
    });

    it('should normalize locale strings', () => {
      const result = getUserPreferredLocale('en_us', null, null);
      expect(result).toBe('en-US');
    });
  });

  describe('safeTranslate', () => {
    const mockMessages = {
      common: {
        save: 'Save',
        cancel: 'Cancel',
      },
      nested: {
        deep: {
          value: 'Deep Value',
        },
      },
    };

    const mockFallbackMessages = {
      common: {
        save: 'Guardar',
        cancel: 'Cancelar',
        missing: 'Faltante',
      },
    };

    beforeEach(() => {
      i18nTelemetry.clear();
    });

    it('should return translation for existing key', () => {
      const result = safeTranslate(mockMessages, 'common.save', 'en-US');
      expect(result).toBe('Save');
    });

    it('should handle nested keys', () => {
      const result = safeTranslate(mockMessages, 'nested.deep.value', 'en-US');
      expect(result).toBe('Deep Value');
    });

    it('should fall back to fallback messages for missing key', () => {
      const result = safeTranslate(
        mockMessages, 
        'common.missing', 
        'en-US', 
        mockFallbackMessages
      );
      expect(result).toBe('Faltante');
    });

    it('should return key for completely missing translation', () => {
      const result = safeTranslate(mockMessages, 'nonexistent.key', 'en-US');
      expect(result).toBe('nonexistent.key');
    });

    it('should log missing keys to telemetry', () => {
      safeTranslate(mockMessages, 'missing.key', 'en-US');
      
      const events = i18nTelemetry.getRecentEvents();
      expect(events).toHaveLength(1);
      expect(events[0].type).toBe('i18n_missing_key');
      expect(events[0].key).toBe('missing.key');
      expect(events[0].locale).toBe('en-US');
    });
  });
});

describe('i18n Telemetry', () => {
  beforeEach(() => {
    i18nTelemetry.clear();
  });

  it('should log missing keys', () => {
    i18nTelemetry.logMissingKey('en-US', 'test.key');
    
    const events = i18nTelemetry.getRecentEvents();
    expect(events).toHaveLength(1);
    expect(events[0].type).toBe('i18n_missing_key');
    expect(events[0].locale).toBe('en-US');
    expect(events[0].key).toBe('test.key');
  });

  it('should log fallback usage', () => {
    i18nTelemetry.logFallbackUsed('es-AR', 'en-US', 'test.key');
    
    const events = i18nTelemetry.getRecentEvents();
    expect(events).toHaveLength(1);
    expect(events[0].type).toBe('i18n_fallback_used');
    expect(events[0].locale).toBe('es-AR');
    expect(events[0].fallbackLocale).toBe('en-US');
  });

  it('should generate missing keys summary', () => {
    i18nTelemetry.logMissingKey('en-US', 'key1');
    i18nTelemetry.logMissingKey('en-US', 'key2');
    i18nTelemetry.logMissingKey('es-AR', 'key1');
    
    const summary = i18nTelemetry.getMissingKeysSummary();
    
    expect(summary['en-US'].count).toBe(2);
    expect(summary['en-US'].keys).toContain('key1');
    expect(summary['en-US'].keys).toContain('key2');
    expect(summary['es-AR'].count).toBe(1);
    expect(summary['es-AR'].keys).toContain('key1');
  });
});

describe('API Errors', () => {
  describe('getLocalizedErrorMessage', () => {
    it('should return localized error message for es-AR', () => {
      const result = getLocalizedErrorMessage('unauthorized', 'es-AR');
      expect(result).toContain('autorizado');
    });

    it('should return localized error message for en-US', () => {
      const result = getLocalizedErrorMessage('unauthorized', 'en-US');
      expect(result).toContain('Unauthorized');
    });

    it('should fall back to en-US for unknown locale', () => {
      const result = getLocalizedErrorMessage('unauthorized', 'fr-FR' as any);
      expect(result).toContain('Unauthorized');
    });

    it('should handle unknown error codes', () => {
      const result = getLocalizedErrorMessage('unknown_error' as any, 'en-US');
      expect(result).toContain('error');
    });
  });
});

describe('Locale Validation', () => {
  it('should validate supported locales', () => {
    const { isValidLocale } = require('@/i18n');
    
    expect(isValidLocale('es-AR')).toBe(true);
    expect(isValidLocale('en-US')).toBe(true);
    expect(isValidLocale('fr-FR')).toBe(false);
    expect(isValidLocale('invalid')).toBe(false);
    expect(isValidLocale('')).toBe(false);
  });

  it('should normalize locale strings', () => {
    const { getLocaleFromString } = require('@/i18n');
    
    expect(getLocaleFromString('es')).toBe('es-AR');
    expect(getLocaleFromString('es-ar')).toBe('es-AR');
    expect(getLocaleFromString('es_AR')).toBe('es-AR');
    expect(getLocaleFromString('en')).toBe('en-US');
    expect(getLocaleFromString('en-us')).toBe('en-US');
    expect(getLocaleFromString('en_US')).toBe('en-US');
    expect(getLocaleFromString('invalid')).toBe('es-AR'); // Default
  });
});
