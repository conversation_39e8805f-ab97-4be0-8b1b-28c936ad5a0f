import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';

// Supported locales
export const locales = ['es-AR', 'en-US'] as const;
export type Locale = typeof locales[number];

// Default locale
export const defaultLocale: Locale = 'es-AR';

// Locale validation
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}

// Get locale from various sources
export function getLocaleFromString(localeString: string): Locale {
  // Handle different locale formats
  const normalized = localeString.toLowerCase().replace('_', '-');
  
  switch (normalized) {
    case 'es':
    case 'es-ar':
    case 'es_ar':
      return 'es-AR';
    case 'en':
    case 'en-us':
    case 'en_us':
      return 'en-US';
    default:
      return defaultLocale;
  }
}

// Next-intl configuration
export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!isValidLocale(locale)) {
    notFound();
  }

  try {
    const messages = (await import(`../locales/${locale}.json`)).default;
    
    return {
      messages,
      timeZone: locale === 'es-AR' ? 'America/Argentina/Buenos_Aires' : 'America/New_York',
      now: new Date(),
      formats: {
        dateTime: {
          short: {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
          },
          medium: {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
          },
          long: {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            second: '2-digit',
          },
        },
        number: {
          currency: {
            style: 'currency',
            currency: locale === 'es-AR' ? 'ARS' : 'USD',
          },
          percent: {
            style: 'percent',
            minimumFractionDigits: 1,
            maximumFractionDigits: 2,
          },
        },
      },
    };
  } catch (error) {
    // Log missing locale file
    console.error(`Failed to load locale ${locale}:`, error);
    
    // Fallback to default locale
    if (locale !== defaultLocale) {
      const fallbackMessages = (await import(`../locales/${defaultLocale}.json`)).default;
      return {
        messages: fallbackMessages,
        timeZone: 'America/Argentina/Buenos_Aires',
        now: new Date(),
      };
    }
    
    throw error;
  }
});
