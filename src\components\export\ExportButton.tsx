'use client';

import { useState } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { 
  DocumentArrowDownIcon, 
  DocumentTextIcon,
  ChevronDownIcon 
} from '@heroicons/react/24/outline';
import { Menu, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import clsx from 'clsx';

import { type Locale } from '@/i18n';
import { 
  exportData, 
  generateExportFilename,
  getLocalizedColumns,
  type CSVProfile,
  CSV_PROFILES,
  type ExportColumn 
} from '@/lib/export-utils';
import { exportElementToPDF, generatePDFFilename } from '@/lib/pdf-utils';

interface ExportButtonProps {
  data: any[];
  columns: ExportColumn[];
  filename?: string;
  elementId?: string; // For PDF export of HTML element
  variant?: 'button' | 'dropdown' | 'split';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
  onExportStart?: () => void;
  onExportComplete?: (type: 'csv' | 'pdf', profile?: CSVProfile) => void;
  onExportError?: (error: Error) => void;
}

export function ExportButton({
  data,
  columns,
  filename,
  elementId,
  variant = 'dropdown',
  size = 'md',
  className,
  disabled = false,
  onExportStart,
  onExportComplete,
  onExportError,
}: ExportButtonProps) {
  const locale = useLocale() as Locale;
  const t = useTranslations('common');
  const tReports = useTranslations('reports');
  const [isExporting, setIsExporting] = useState(false);

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
  };

  const handleCSVExport = async (profile: CSVProfile) => {
    try {
      setIsExporting(true);
      onExportStart?.();

      // Get localized column labels
      const columnTranslations = columns.reduce((acc, col) => {
        acc[col.key] = col.label; // You might want to translate these
        return acc;
      }, {} as Record<string, string>);

      const localizedColumns = getLocalizedColumns(columns, locale, columnTranslations);
      
      const exportFilename = filename || generateExportFilename('export', locale);

      await exportData(data, localizedColumns, {
        locale,
        profile,
        filename: exportFilename,
        includeHeaders: true,
      });

      onExportComplete?.('csv', profile);
    } catch (error) {
      console.error('CSV export failed:', error);
      onExportError?.(error as Error);
    } finally {
      setIsExporting(false);
    }
  };

  const handlePDFExport = async () => {
    if (!elementId) {
      onExportError?.(new Error('Element ID required for PDF export'));
      return;
    }

    try {
      setIsExporting(true);
      onExportStart?.();

      const pdfFilename = filename 
        ? filename.replace(/\.[^/.]+$/, '.pdf')
        : generatePDFFilename('export', locale);

      await exportElementToPDF(elementId, {
        locale,
        title: tReports('exportedReport'),
        filename: pdfFilename,
        includeTimestamp: true,
        includePageNumbers: true,
      });

      onExportComplete?.('pdf');
    } catch (error) {
      console.error('PDF export failed:', error);
      onExportError?.(error as Error);
    } finally {
      setIsExporting(false);
    }
  };

  if (variant === 'button') {
    return (
      <button
        onClick={() => handleCSVExport('standard')}
        disabled={disabled || isExporting}
        className={clsx(
          'inline-flex items-center border border-gray-300 rounded-md font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed',
          sizeClasses[size],
          className
        )}
        data-testid="export-csv"
      >
        <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
        {isExporting ? t('loading') : t('export')}
      </button>
    );
  }

  if (variant === 'split') {
    return (
      <div className="relative inline-flex">
        {/* Main export button */}
        <button
          onClick={() => handleCSVExport('standard')}
          disabled={disabled || isExporting}
          className={clsx(
            'inline-flex items-center border border-gray-300 rounded-l-md font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed',
            sizeClasses[size],
            className
          )}
        >
          <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
          {isExporting ? t('loading') : t('exportCsv')}
        </button>

        {/* Dropdown for additional options */}
        <Menu as="div" className="relative">
          <Menu.Button
            disabled={disabled || isExporting}
            className={clsx(
              'inline-flex items-center border-l-0 border border-gray-300 rounded-r-md font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed',
              sizeClasses[size].replace('px-4', 'px-2').replace('px-6', 'px-3')
            )}
          >
            <ChevronDownIcon className="h-4 w-4" />
          </Menu.Button>

          <Transition
            as={Fragment}
            enter="transition ease-out duration-100"
            enterFrom="transform opacity-0 scale-95"
            enterTo="transform opacity-100 scale-100"
            leave="transition ease-in duration-75"
            leaveFrom="transform opacity-100 scale-100"
            leaveTo="transform opacity-0 scale-95"
          >
            <Menu.Items className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
              <div className="py-1">
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={() => handleCSVExport('excelAR')}
                      className={clsx(
                        'flex w-full items-center px-4 py-2 text-sm',
                        active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                      )}
                      data-testid="csv-excel-ar"
                    >
                      <DocumentTextIcon className="h-4 w-4 mr-3" />
                      {tReports('csvExcelAr')}
                    </button>
                  )}
                </Menu.Item>
                
                {elementId && (
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={handlePDFExport}
                        className={clsx(
                          'flex w-full items-center px-4 py-2 text-sm',
                          active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                        )}
                        data-testid="export-pdf"
                      >
                        <DocumentTextIcon className="h-4 w-4 mr-3" />
                        {t('exportPdf')}
                      </button>
                    )}
                  </Menu.Item>
                )}
              </div>
            </Menu.Items>
          </Transition>
        </Menu>
      </div>
    );
  }

  // Default dropdown variant
  return (
    <Menu as="div" className="relative inline-block text-left">
      <Menu.Button
        disabled={disabled || isExporting}
        className={clsx(
          'inline-flex items-center justify-center border border-gray-300 rounded-md font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed',
          sizeClasses[size],
          className
        )}
        data-testid="export-dropdown"
      >
        <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
        {isExporting ? t('loading') : t('export')}
        <ChevronDownIcon className="h-4 w-4 ml-2" />
      </Menu.Button>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="py-1" data-testid="csv-format-options">
            <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide">
              {tReports('csvFormats')}
            </div>
            
            <Menu.Item>
              {({ active }) => (
                <button
                  onClick={() => handleCSVExport('standard')}
                  className={clsx(
                    'flex w-full items-center px-4 py-2 text-sm',
                    active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                  )}
                  data-testid="csv-standard"
                >
                  <DocumentTextIcon className="h-4 w-4 mr-3" />
                  {tReports('csvStandard')}
                </button>
              )}
            </Menu.Item>
            
            <Menu.Item>
              {({ active }) => (
                <button
                  onClick={() => handleCSVExport('excelAR')}
                  className={clsx(
                    'flex w-full items-center px-4 py-2 text-sm',
                    active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                  )}
                  data-testid="csv-excel-ar"
                >
                  <DocumentTextIcon className="h-4 w-4 mr-3" />
                  {tReports('csvExcelAr')}
                </button>
              )}
            </Menu.Item>

            {elementId && (
              <>
                <div className="border-t border-gray-100 my-1"></div>
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide">
                  {tReports('pdfFormats')}
                </div>
                
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={handlePDFExport}
                      className={clsx(
                        'flex w-full items-center px-4 py-2 text-sm',
                        active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                      )}
                      data-testid="export-pdf"
                    >
                      <DocumentTextIcon className="h-4 w-4 mr-3" />
                      {t('exportPdf')}
                    </button>
                  )}
                </Menu.Item>
              </>
            )}
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  );
}

// Specialized export buttons for common use cases

interface ProductExportButtonProps {
  products: any[];
  className?: string;
}

export function ProductExportButton({ products, className }: ProductExportButtonProps) {
  const locale = useLocale() as Locale;
  const t = useTranslations('products');

  const columns: ExportColumn[] = [
    { key: 'sku', label: t('sku'), type: 'string' },
    { key: 'name', label: t('name'), type: 'string' },
    { key: 'category', label: t('category'), type: 'string' },
    { key: 'retailPrice', label: t('retailPrice'), type: 'currency' },
    { key: 'wholesalePrice', label: t('wholesalePrice'), type: 'currency' },
    { key: 'currentStock', label: t('currentStock'), type: 'number' },
    { key: 'minStock', label: t('minStock'), type: 'number' },
    { key: 'isActive', label: t('active'), type: 'boolean' },
  ];

  return (
    <ExportButton
      data={products}
      columns={columns}
      filename={generateExportFilename('products', locale)}
      variant="dropdown"
      className={className}
    />
  );
}

interface SalesExportButtonProps {
  sales: any[];
  className?: string;
}

export function SalesExportButton({ sales, className }: SalesExportButtonProps) {
  const locale = useLocale() as Locale;
  const t = useTranslations('sales');

  const columns: ExportColumn[] = [
    { key: 'number', label: t('saleNumber'), type: 'string' },
    { key: 'date', label: t('saleDate'), type: 'date' },
    { key: 'customerName', label: t('customerName'), type: 'string' },
    { key: 'totalAmount', label: t('totalPrice'), type: 'currency' },
    { key: 'paymentMethod', label: t('paymentMethod'), type: 'string' },
    { key: 'items', label: t('items'), type: 'number' },
  ];

  return (
    <ExportButton
      data={sales}
      columns={columns}
      filename={generateExportFilename('sales', locale)}
      variant="dropdown"
      className={className}
    />
  );
}
