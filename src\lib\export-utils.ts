/**
 * Export Utilities with Localization Support
 * Handles CSV and PDF exports with proper locale formatting
 */

import { type Locale } from '@/i18n';
import { formatCurrency, formatNumber, formatDate, getLocaleSeparators } from '@/lib/intl-formatters';

// CSV Export Profiles
export const CSV_PROFILES = {
  standard: {
    id: 'standard',
    name: 'CSV Standard (,)',
    delimiter: ',',
    decimal: '.',
    thousands: '',
    encoding: 'utf-8',
    bom: false,
  },
  excelAR: {
    id: 'excel-ar',
    name: 'CSV Excel Argentina (;)',
    delimiter: ';',
    decimal: ',',
    thousands: '.',
    encoding: 'utf-8',
    bom: true, // Excel needs BOM for proper UTF-8 handling
  },
} as const;

export type CSVProfile = keyof typeof CSV_PROFILES;

interface ExportColumn {
  key: string;
  label: string;
  type: 'string' | 'number' | 'currency' | 'date' | 'boolean';
  formatter?: (value: any, locale: Locale) => string;
}

interface ExportOptions {
  locale: Locale;
  profile: CSVProfile;
  filename?: string;
  includeHeaders?: boolean;
  dateRange?: {
    from: Date;
    to: Date;
  };
}

/**
 * Format value according to column type and locale
 */
function formatExportValue(
  value: any,
  column: ExportColumn,
  locale: Locale,
  profile: typeof CSV_PROFILES[CSVProfile]
): string {
  if (value === null || value === undefined) {
    return '';
  }

  // Use custom formatter if provided
  if (column.formatter) {
    return column.formatter(value, locale);
  }

  switch (column.type) {
    case 'currency':
      const formatted = formatCurrency(value, locale, 'ARS');
      // Remove currency symbol for CSV and apply locale-specific decimal separator
      const numericValue = typeof value === 'number' ? value : parseFloat(value);
      return numericValue.toFixed(2).replace('.', profile.decimal);

    case 'number':
      const numValue = typeof value === 'number' ? value : parseFloat(value);
      if (isNaN(numValue)) return '';
      return numValue.toString().replace('.', profile.decimal);

    case 'date':
      if (!value) return '';
      const date = new Date(value);
      return formatDate(date, locale, { 
        year: 'numeric', 
        month: '2-digit', 
        day: '2-digit' 
      });

    case 'boolean':
      return value ? (locale === 'es-AR' ? 'Sí' : 'Yes') : (locale === 'es-AR' ? 'No' : 'No');

    default:
      return String(value);
  }
}

/**
 * Escape CSV field if needed
 */
function escapeCSVField(value: string, delimiter: string): string {
  // If the value contains delimiter, quotes, or newlines, wrap in quotes and escape internal quotes
  if (value.includes(delimiter) || value.includes('"') || value.includes('\n') || value.includes('\r')) {
    return `"${value.replace(/"/g, '""')}"`;
  }
  return value;
}

/**
 * Generate CSV content from data
 */
export function generateCSV(
  data: any[],
  columns: ExportColumn[],
  options: ExportOptions
): string {
  const profile = CSV_PROFILES[options.profile];
  const { delimiter } = profile;
  const lines: string[] = [];

  // Add headers if requested
  if (options.includeHeaders !== false) {
    const headers = columns.map(col => escapeCSVField(col.label, delimiter));
    lines.push(headers.join(delimiter));
  }

  // Add data rows
  for (const row of data) {
    const values = columns.map(column => {
      const rawValue = row[column.key];
      const formattedValue = formatExportValue(rawValue, column, options.locale, profile);
      return escapeCSVField(formattedValue, delimiter);
    });
    lines.push(values.join(delimiter));
  }

  let csvContent = lines.join('\n');

  // Add BOM for Excel compatibility if needed
  if (profile.bom) {
    csvContent = '\uFEFF' + csvContent;
  }

  return csvContent;
}

/**
 * Download CSV file
 */
export function downloadCSV(
  csvContent: string,
  filename: string,
  profile: CSVProfile = 'standard'
): void {
  const profileConfig = CSV_PROFILES[profile];
  const mimeType = `text/csv;charset=${profileConfig.encoding}`;
  
  const blob = new Blob([csvContent], { type: mimeType });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename.endsWith('.csv') ? filename : `${filename}.csv`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
}

/**
 * Generate filename with locale and timestamp
 */
export function generateExportFilename(
  baseName: string,
  locale: Locale,
  extension: 'csv' | 'pdf' = 'csv',
  includeTimestamp: boolean = true
): string {
  const timestamp = includeTimestamp 
    ? new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')
    : '';
  
  const localeSuffix = locale === 'es-AR' ? 'es' : 'en';
  
  const parts = [baseName, localeSuffix, timestamp].filter(Boolean);
  return `${parts.join('_')}.${extension}`;
}

// Predefined column configurations for common exports

export const PRODUCT_EXPORT_COLUMNS: ExportColumn[] = [
  { key: 'sku', label: 'SKU', type: 'string' },
  { key: 'name', label: 'Nombre', type: 'string' },
  { key: 'category', label: 'Categoría', type: 'string' },
  { key: 'retailPrice', label: 'Precio Minorista', type: 'currency' },
  { key: 'wholesalePrice', label: 'Precio Mayorista', type: 'currency' },
  { key: 'currentStock', label: 'Stock Actual', type: 'number' },
  { key: 'minStock', label: 'Stock Mínimo', type: 'number' },
  { key: 'isActive', label: 'Activo', type: 'boolean' },
];

export const SALES_EXPORT_COLUMNS: ExportColumn[] = [
  { key: 'number', label: 'Número', type: 'string' },
  { key: 'date', label: 'Fecha', type: 'date' },
  { key: 'customerName', label: 'Cliente', type: 'string' },
  { key: 'totalAmount', label: 'Total', type: 'currency' },
  { key: 'paymentMethod', label: 'Método de Pago', type: 'string' },
  { key: 'items', label: 'Artículos', type: 'number' },
];

export const INVENTORY_EXPORT_COLUMNS: ExportColumn[] = [
  { key: 'sku', label: 'SKU', type: 'string' },
  { key: 'name', label: 'Producto', type: 'string' },
  { key: 'category', label: 'Categoría', type: 'string' },
  { key: 'onHand', label: 'En Stock', type: 'number' },
  { key: 'avgCost', label: 'Costo Promedio', type: 'currency' },
  { key: 'totalValue', label: 'Valor Total', type: 'currency' },
  { key: 'lastMovement', label: 'Último Movimiento', type: 'date' },
];

export const CASH_EXPORT_COLUMNS: ExportColumn[] = [
  { key: 'date', label: 'Fecha', type: 'date' },
  { key: 'openingCash', label: 'Efectivo Apertura', type: 'currency' },
  { key: 'totalSales', label: 'Total Ventas', type: 'currency' },
  { key: 'totalExpenses', label: 'Total Gastos', type: 'currency' },
  { key: 'closingCash', label: 'Efectivo Cierre', type: 'currency' },
  { key: 'difference', label: 'Diferencia', type: 'currency' },
  { key: 'isApproved', label: 'Aprobado', type: 'boolean' },
];

/**
 * Get localized column definitions
 */
export function getLocalizedColumns(
  baseColumns: ExportColumn[],
  locale: Locale,
  translations: Record<string, string>
): ExportColumn[] {
  return baseColumns.map(column => ({
    ...column,
    label: translations[column.key] || column.label,
  }));
}

/**
 * Export data with proper localization
 */
export async function exportData(
  data: any[],
  columns: ExportColumn[],
  options: ExportOptions
): Promise<void> {
  try {
    const csvContent = generateCSV(data, columns, options);
    const filename = options.filename || generateExportFilename('export', options.locale);
    
    downloadCSV(csvContent, filename, options.profile);
    
    // Log export event for analytics
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'export_csv', {
        event_category: 'export',
        event_label: options.profile,
        value: data.length,
      });
    }
  } catch (error) {
    console.error('Export failed:', error);
    throw new Error('Failed to export data');
  }
}

/**
 * Validate export data
 */
export function validateExportData(
  data: any[],
  columns: ExportColumn[]
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!Array.isArray(data)) {
    errors.push('Data must be an array');
    return { isValid: false, errors };
  }
  
  if (data.length === 0) {
    errors.push('No data to export');
    return { isValid: false, errors };
  }
  
  if (!Array.isArray(columns) || columns.length === 0) {
    errors.push('Column definitions are required');
    return { isValid: false, errors };
  }
  
  // Check if all required columns exist in data
  const sampleRow = data[0];
  const missingColumns = columns
    .map(col => col.key)
    .filter(key => !(key in sampleRow));
  
  if (missingColumns.length > 0) {
    errors.push(`Missing columns in data: ${missingColumns.join(', ')}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}
