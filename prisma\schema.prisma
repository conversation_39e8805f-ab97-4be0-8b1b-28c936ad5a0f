// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Supported locales enum
enum Locale {
  es_AR
  en_US
}

// User roles enum
enum UserRole {
  OWNER
  MANAGER
  CLERK
  CASHIER
}

// Tenant (Business) model
model Tenant {
  id             String   @id @default(cuid())
  name           String
  defaultLocale  Locale   @default(es_AR) @map("default_locale")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  users          User[]
  categories     Category[]
  expenseTypes   ExpenseType[]
  products       Product[]
  purchases      Purchase[]
  sales          Sale[]
  cashClosings   CashClosing[]
  
  @@map("tenants")
}

// User model with locale preference
model User {
  id               String   @id @default(cuid())
  email            String   @unique
  name             String
  role             UserRole @default(CLERK)
  localePreference Locale?  @map("locale_preference")
  isActive         Boolean  @default(true) @map("is_active")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  tenantId         String   @map("tenant_id")
  tenant           Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  // Activity tracking
  purchases        Purchase[]
  sales            Sale[]
  cashClosings     CashClosing[]
  
  @@map("users")
}

// Category model with optional i18n support
model Category {
  id        String   @id @default(cuid())
  name      String
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  tenantId  String   @map("tenant_id")
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  products  Product[]
  
  // i18n support
  translations CategoryI18n[]
  
  @@map("categories")
}

// Category internationalization table
model CategoryI18n {
  id          String @id @default(cuid())
  locale      Locale
  displayName String @map("display_name")
  
  // Relations
  categoryId  String @map("category_id")
  category    Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  
  @@unique([categoryId, locale])
  @@map("category_i18n")
}

// Expense Type model with optional i18n support
model ExpenseType {
  id        String   @id @default(cuid())
  name      String
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  tenantId  String   @map("tenant_id")
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  // i18n support
  translations ExpenseTypeI18n[]
  
  @@map("expense_types")
}

// Expense Type internationalization table
model ExpenseTypeI18n {
  id            String @id @default(cuid())
  locale        Locale
  displayName   String @map("display_name")
  
  // Relations
  expenseTypeId String @map("expense_type_id")
  expenseType   ExpenseType @relation(fields: [expenseTypeId], references: [id], onDelete: Cascade)
  
  @@unique([expenseTypeId, locale])
  @@map("expense_type_i18n")
}

// Product model (core entity)
model Product {
  id          String   @id @default(cuid())
  sku         String   @unique
  barcode     String?
  name        String
  description String?
  costMethod  String   @default("avg") @map("cost_method") // avg, fifo, lifo
  avgCost     Decimal  @default(0) @map("avg_cost") @db.Decimal(10, 2)
  retailPrice Decimal  @default(0) @map("retail_price") @db.Decimal(10, 2)
  wholesalePrice Decimal @default(0) @map("wholesale_price") @db.Decimal(10, 2)
  currentStock Int     @default(0) @map("current_stock")
  minStock    Int      @default(0) @map("min_stock")
  reorderPoint Int?    @map("reorder_point")
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  tenantId    String   @map("tenant_id")
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  categoryId  String?  @map("category_id")
  category    Category? @relation(fields: [categoryId], references: [id])
  
  // Variants and transactions
  variants    ProductVariant[]
  purchaseItems PurchaseItem[]
  saleItems   SaleItem[]
  
  @@map("products")
}

// Product variants (size, color, etc.)
model ProductVariant {
  id        String   @id @default(cuid())
  name      String   // e.g., "Large", "Red", "XL"
  value     String   // e.g., "L", "#FF0000", "XL"
  type      String   // e.g., "size", "color"
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  productId String  @map("product_id")
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@map("product_variants")
}

// Purchase model
model Purchase {
  id          String   @id @default(cuid())
  number      String   // Purchase order number
  supplierName String? @map("supplier_name")
  totalAmount Decimal  @map("total_amount") @db.Decimal(10, 2)
  notes       String?
  purchaseDate DateTime @map("purchase_date")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  tenantId    String   @map("tenant_id")
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdById String   @map("created_by_id")
  createdBy   User     @relation(fields: [createdById], references: [id])
  
  items       PurchaseItem[]
  
  @@map("purchases")
}

// Purchase items
model PurchaseItem {
  id         String   @id @default(cuid())
  quantity   Int
  unitCost   Decimal  @map("unit_cost") @db.Decimal(10, 2)
  totalCost  Decimal  @map("total_cost") @db.Decimal(10, 2)

  // Relations
  purchaseId String   @map("purchase_id")
  purchase   Purchase @relation(fields: [purchaseId], references: [id], onDelete: Cascade)
  productId  String   @map("product_id")
  product    Product  @relation(fields: [productId], references: [id])
  
  @@map("purchase_items")
}

// Sale model
model Sale {
  id          String   @id @default(cuid())
  number      String   // Sale receipt number
  customerName String? @map("customer_name")
  totalAmount Decimal  @map("total_amount") @db.Decimal(10, 2)
  notes       String?
  saleDate    DateTime @map("sale_date")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  tenantId    String   @map("tenant_id")
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdById String   @map("created_by_id")
  createdBy   User     @relation(fields: [createdById], references: [id])
  
  items       SaleItem[]
  
  @@map("sales")
}

// Sale items
model SaleItem {
  id         String   @id @default(cuid())
  quantity   Int
  unitPrice  Decimal  @map("unit_price") @db.Decimal(10, 2)
  totalPrice Decimal  @map("total_price") @db.Decimal(10, 2)

  // Relations
  saleId     String   @map("sale_id")
  sale       Sale     @relation(fields: [saleId], references: [id], onDelete: Cascade)
  productId  String   @map("product_id")
  product    Product  @relation(fields: [productId], references: [id])
  
  @@map("sale_items")
}

// Cash closing model
model CashClosing {
  id            String   @id @default(cuid())
  date          DateTime
  openingCash   Decimal  @map("opening_cash") @db.Decimal(10, 2)
  closingCash   Decimal  @map("closing_cash") @db.Decimal(10, 2)
  totalSales    Decimal  @map("total_sales") @db.Decimal(10, 2)
  totalExpenses Decimal  @map("total_expenses") @db.Decimal(10, 2)
  notes         String?
  isApproved    Boolean  @default(false) @map("is_approved")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  tenantId      String   @map("tenant_id")
  tenant        Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdById   String   @map("created_by_id")
  createdBy     User     @relation(fields: [createdById], references: [id])
  
  @@map("cash_closings")
}
