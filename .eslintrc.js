module.exports = {
  extends: [
    'next/core-web-vitals',
    '@typescript-eslint/recommended',
  ],
  parser: '@typescript-eslint/parser',
  plugins: [
    '@typescript-eslint',
    'formatjs',
  ],
  rules: {
    // i18n specific rules
    'formatjs/no-offset': 'error',
    'formatjs/enforce-default-message': 'error',
    'formatjs/enforce-placeholders': 'error',
    'formatjs/no-multiple-whitespaces': 'error',
    'formatjs/no-complex-selectors': 'error',
    
    // Custom rule to detect hardcoded strings
    'no-restricted-syntax': [
      'error',
      {
        selector: 'JSXText[value=/[A-Z][a-z]{2,}/]',
        message: 'Hardcoded text detected. Use translation keys instead.',
      },
      {
        selector: 'Literal[value=/^[A-Z][a-z]{2,}/]',
        message: 'Hardcoded string detected. Use translation keys instead.',
      },
    ],
    
    // TypeScript specific rules
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/prefer-const': 'error',
    
    // General rules
    'prefer-const': 'error',
    'no-var': 'error',
    'no-console': ['warn', { allow: ['warn', 'error'] }],
  },
  overrides: [
    {
      files: ['**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts', '**/*.spec.tsx'],
      rules: {
        'no-restricted-syntax': 'off', // Allow hardcoded strings in tests
        '@typescript-eslint/no-explicit-any': 'off',
      },
    },
    {
      files: ['scripts/**/*.js'],
      rules: {
        'no-console': 'off', // Allow console in scripts
        '@typescript-eslint/no-var-requires': 'off',
      },
    },
  ],
  ignorePatterns: [
    'node_modules/',
    '.next/',
    'dist/',
    'build/',
    'coverage/',
    'test-results/',
  ],
};
