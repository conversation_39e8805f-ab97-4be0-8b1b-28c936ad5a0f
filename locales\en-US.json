{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "select": "Select", "clear": "Clear", "reset": "Reset", "refresh": "Refresh", "view": "View", "print": "Print", "download": "Download", "upload": "Upload", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "required": "Required", "optional": "Optional", "total": "Total", "subtotal": "Subtotal", "quantity": "Quantity", "price": "Price", "amount": "Amount", "date": "Date", "time": "Time", "name": "Name", "description": "Description", "status": "Status", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled"}, "navigation": {"dashboard": "Dashboard", "products": "Products", "inventory": "Inventory", "sales": "Sales", "purchases": "Purchases", "cash": "Cash", "reports": "Reports", "settings": "Settings", "profile": "Profile", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "email": "Email", "password": "Password", "forgotPassword": "Forgot your password?", "rememberMe": "Remember me", "signIn": "Sign In", "signUp": "Sign Up", "createAccount": "Create Account", "welcomeBack": "Welcome back!", "invalidCredentials": "Invalid credentials", "accountCreated": "Account created successfully"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome, {name}", "overview": "Overview", "todaySales": "Today's Sales", "totalRevenue": "Total Revenue", "lowStock": "Low Stock", "reorderNeeded": "Reorder Needed", "salesChart": "Sales Chart", "topProducts": "Top Products", "recentTransactions": "Recent Transactions", "cashFlow": "Cash Flow", "profitMargin": "<PERSON><PERSON>", "inventoryValue": "Inventory Value", "pendingOrders": "Pending Orders", "todaysSummary": "Today's Summary", "quickActions": "Quick Actions", "newSale": "New Sale", "createNewSale": "Create new sale", "receiveStock": "Receive Stock", "receiveNewStock": "Receive new stock", "cashClosing": "Cash Closing", "performCashClosing": "Perform cash closing", "viewReports": "View Reports", "accessDetailedReports": "Access detailed reports", "dataStaleWarning": "Data Stale Warning", "dataStaleDescription": "Some data may be stale. Sync to get the latest information."}, "products": {"title": "Products", "addProduct": "Add Product", "editProduct": "Edit Product", "deleteProduct": "Delete Product", "productList": "Product List", "sku": "SKU", "barcode": "Barcode", "category": "Category", "cost": "Cost", "retailPrice": "Retail Price", "wholesalePrice": "Wholesale Price", "currentStock": "Current Stock", "minStock": "<PERSON>", "reorderPoint": "Reorder Point", "variants": "Variants", "addVariant": "<PERSON><PERSON>", "variantType": "Variant Type", "variantValue": "Variant Value", "size": "Size", "color": "Color", "material": "Material", "brand": "Brand", "supplier": "Supplier", "costMethod": "Cost Method", "avgCost": "Average Cost", "fifo": "FIFO (First In, First Out)", "lifo": "LIFO (Last In, First Out)", "productCreated": "Product created successfully", "productUpdated": "Product updated successfully", "productDeleted": "Product deleted successfully", "confirmDelete": "Are you sure you want to delete this product?", "bulkImport": "Bulk Import", "csvTemplate": "CSV Template", "importSuccess": "Import completed successfully", "importError": "Import error"}, "inventory": {"title": "Inventory", "stockMovements": "Stock Movements", "adjustment": "Adjustment", "transfer": "Transfer", "lowStockAlert": "Low Stock Alert", "reorderAlert": "Reorder Alert", "stockValue": "Stock Value", "turnoverRate": "Turnover Rate", "deadStock": "Dead Stock", "fastMoving": "Fast Moving", "slowMoving": "Slow Moving", "outOfStock": "Out of Stock", "inStock": "In Stock", "reserved": "Reserved", "available": "Available", "onOrder": "On Order", "damaged": "Damaged", "expired": "Expired", "returned": "Returned"}, "sales": {"title": "Sales", "newSale": "New Sale", "saleNumber": "Sale Number", "customer": "Customer", "customerName": "Customer Name", "saleDate": "Sale Date", "items": "Items", "addItem": "Add Item", "unitPrice": "Unit Price", "totalPrice": "Total Price", "discount": "Discount", "tax": "Tax", "grandTotal": "Grand Total", "paymentMethod": "Payment Method", "cash": "Cash", "card": "Card", "transfer": "Transfer", "credit": "Credit", "receipt": "Receipt", "printReceipt": "Print Receipt", "emailReceipt": "Email Receipt", "whatsappReceipt": "WhatsApp Receipt", "saleCompleted": "Sale completed successfully", "saleVoided": "Sale voided", "refund": "Refund", "partialRefund": "Partial Refund", "fullRefund": "Full Refund", "dailySales": "Daily Sales", "weeklySales": "Weekly Sales", "monthlySales": "Monthly Sales", "salesReport": "Sales Report", "topSellingProducts": "Top Selling Products", "salesByCategory": "Sales by Category", "salesTrend": "Sales Trend"}, "purchases": {"title": "Purchases", "newPurchase": "New Purchase", "purchaseOrder": "Purchase Order", "purchaseNumber": "Purchase Number", "supplier": "Supplier", "supplierName": "Supplier Name", "purchaseDate": "Purchase Date", "deliveryDate": "Delivery Date", "unitCost": "Unit Cost", "totalCost": "Total Cost", "received": "Received", "pending": "Pending", "cancelled": "Cancelled", "purchaseCreated": "Purchase created successfully", "purchaseUpdated": "Purchase updated successfully", "receiveItems": "Receive Items", "partialReceive": "Partial Receive", "fullReceive": "Full Receive", "purchaseReport": "Purchase Report", "supplierReport": "Supplier Report", "costAnalysis": "Cost Analysis"}, "cash": {"title": "Cash", "cashClosing": "Cash Closing", "openingCash": "Opening Cash", "closingCash": "Closing Cash", "totalSales": "Total Sales", "totalExpenses": "Total Expenses", "cashDifference": "Cash Difference", "expectedCash": "Expected Cash", "actualCash": "Actual Cash", "shortage": "Shortage", "overage": "Overage", "notes": "Notes", "approved": "Approved", "pending": "Pending", "rejected": "Rejected", "cashFlow": "Cash Flow", "dailyCash": "Daily Cash", "weeklyCash": "Weekly Cash", "monthlyCash": "Monthly Cash", "cashReport": "Cash Report", "expense": "Expense", "income": "Income", "expenseType": "Expense Type", "addExpense": "Add Expense", "expenseAmount": "Expense Amount", "expenseDescription": "Expense Description"}, "reports": {"title": "Reports", "salesReport": "Sales Report", "inventoryReport": "Inventory Report", "purchaseReport": "Purchase Report", "cashReport": "Cash Report", "profitLoss": "Profit & Loss", "balanceSheet": "Balance Sheet", "dateRange": "Date Range", "fromDate": "From", "toDate": "To", "generateReport": "Generate Report", "exportPdf": "Export PDF", "exportCsv": "Export CSV", "exportExcel": "Export Excel", "csvStandard": "CSV Standard (,)", "csvExcelAr": "CSV Excel Argentina (;)", "csvFormats": "CSV Formats", "pdfFormats": "PDF Formats", "exportedReport": "Exported Report", "reportGenerated": "Report generated successfully", "noDataFound": "No data found for the selected period", "summary": "Summary", "details": "Details", "charts": "Charts", "kpi": "Key Performance Indicators", "performance": "Performance", "trends": "Trends", "comparison": "Comparison", "forecast": "Forecast"}, "settings": {"title": "Settings", "profile": "Profile", "account": "Account", "preferences": "Preferences", "language": "Language", "timezone": "Timezone", "currency": "<PERSON><PERSON><PERSON><PERSON>", "dateFormat": "Date Format", "timeFormat": "Time Format", "numberFormat": "Number Format", "notifications": "Notifications", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "smsNotifications": "SMS Notifications", "security": "Security", "changePassword": "Change Password", "twoFactorAuth": "Two-Factor Authentication", "loginHistory": "Login History", "backup": "Backup", "dataExport": "Data Export", "dataImport": "Data Import", "systemSettings": "System Settings", "userManagement": "User Management", "rolePermissions": "Role Permissions", "integrations": "Integrations", "apiKeys": "API Keys", "webhooks": "Webhooks", "settingsSaved": "Setting<PERSON> saved successfully"}, "errors": {"general": "An unexpected error occurred", "network": "Network connection error", "validation": "Validation error", "unauthorized": "Unauthorized", "forbidden": "Access forbidden", "notFound": "Not found", "serverError": "Internal server error", "timeout": "Request timeout", "invalidInput": "Invalid input", "duplicateEntry": "Duplicate entry", "insufficientStock": "Insufficient stock", "invalidBarcode": "Invalid barcode", "invalidSku": "Invalid SKU", "priceRequired": "Price is required", "quantityRequired": "Quantity is required", "customerRequired": "Customer is required", "productRequired": "Product is required", "dateRequired": "Date is required", "amountRequired": "Amount is required"}, "validation": {"required": "This field is required", "email": "Please enter a valid email", "minLength": "Minimum {min} characters", "maxLength": "Maximum {max} characters", "min": "Minimum value: {min}", "max": "Maximum value: {max}", "positive": "Must be a positive number", "integer": "Must be an integer", "decimal": "Must be a valid decimal number", "unique": "This value already exists", "pattern": "Invalid format", "date": "Please enter a valid date", "time": "Please enter a valid time", "url": "Please enter a valid URL", "phone": "Please enter a valid phone number"}, "units": {"piece": "Piece", "pieces": "Pieces", "kg": "kg", "g": "g", "l": "l", "ml": "ml", "m": "m", "cm": "cm", "mm": "mm", "box": "Box", "boxes": "Boxes", "pack": "Pack", "packs": "Packs", "dozen": "<PERSON><PERSON>", "dozens": "<PERSON><PERSON><PERSON>"}, "time": {"now": "Now", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This Week", "lastWeek": "Last Week", "nextWeek": "Next Week", "thisMonth": "This Month", "lastMonth": "Last Month", "nextMonth": "Next Month", "thisYear": "This Year", "lastYear": "Last Year", "nextYear": "Next Year", "minute": "minute", "minutes": "minutes", "hour": "hour", "hours": "hours", "day": "day", "days": "days", "week": "week", "weeks": "weeks", "month": "month", "months": "months", "year": "year", "years": "years", "ago": "ago", "in": "in"}, "kpi": {"salesTotal": "Total Sales", "salesGrowth": "Sales Growth", "profitMargin": "<PERSON><PERSON>", "inventoryTurnover": "Inventory Turnover", "averageOrderValue": "Average Order Value", "customerRetention": "Customer Retention", "costOfGoodsSold": "Cost of Goods Sold", "grossProfit": "Gross Profit", "netProfit": "Net Profit", "returnOnInvestment": "Return on Investment", "cashFlowPositive": "Positive Cash Flow", "cashFlowNegative": "Negative Cash Flow", "stockoutRate": "Stockout Rate", "fillRate": "Fill Rate", "leadTime": "Lead Time", "orderAccuracy": "Order Accuracy", "customerSatisfaction": "Customer Satisfaction", "employeeProductivity": "Employee Productivity", "dataStale": "Data Stale", "stale": "Stale", "fromPrevious": "from previous", "target": "Target", "progress": "Progress"}, "charts": {"salesOverTime": "Sales Over Time", "topProducts": "Top Products", "categoryBreakdown": "Category Breakdown", "monthlyTrends": "Monthly Trends", "quarterlyComparison": "Quarterly Comparison", "yearOverYear": "Year Over Year", "dailyPerformance": "Daily Performance", "weeklyAnalysis": "Weekly Analysis", "profitAnalysis": "Profit Analysis", "costAnalysis": "Cost Analysis", "inventoryLevels": "Inventory Levels", "stockMovements": "Stock Movements", "salesForecast": "Sales Forecast", "demandForecast": "De<PERSON>", "seasonalTrends": "Seasonal Trends", "performanceMetrics": "Performance Metrics"}}