/**
 * Export Utilities Tests
 * Tests for CSV and PDF export functionality with localization
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import {
  generateCSV,
  generateExportFilename,
  getLocalizedColumns,
  validateExportData,
  CSV_PROFILES,
  PRODUCT_EXPORT_COLUMNS,
  SALES_EXPORT_COLUMNS,
} from '@/lib/export-utils';

// Mock data
const mockProducts = [
  {
    sku: 'PROD-001',
    name: 'Producto Test',
    category: 'Categoría A',
    retailPrice: 1500.50,
    wholesalePrice: 1200.00,
    currentStock: 25,
    minStock: 5,
    isActive: true,
  },
  {
    sku: 'PROD-002',
    name: 'Test Product',
    category: 'Category B',
    retailPrice: 2750.75,
    wholesalePrice: 2200.00,
    currentStock: 0,
    minStock: 10,
    isActive: false,
  },
];

const mockSales = [
  {
    number: 'SALE-001',
    date: '2023-12-25T10:30:00Z',
    customerName: '<PERSON>',
    totalAmount: 3000.00,
    paymentMethod: 'Efectivo',
    items: 2,
  },
  {
    number: 'SALE-002',
    date: '2023-12-25T14:15:00Z',
    customerName: '<PERSON>',
    totalAmount: 1500.50,
    paymentMethod: 'Tarjeta',
    items: 1,
  },
];

describe('Export Utils', () => {
  describe('generateCSV', () => {
    it('should generate CSV with standard profile (es-AR)', () => {
      const csv = generateCSV(mockProducts, PRODUCT_EXPORT_COLUMNS, {
        locale: 'es-AR',
        profile: 'standard',
        includeHeaders: true,
      });

      const lines = csv.split('\n');
      
      // Check headers
      expect(lines[0]).toContain('SKU');
      expect(lines[0]).toContain('Nombre');
      
      // Check data formatting
      expect(lines[1]).toContain('PROD-001');
      expect(lines[1]).toContain('1500,50'); // Decimal comma for es-AR
      expect(lines[1]).toContain('Sí'); // Boolean in Spanish
    });

    it('should generate CSV with Excel Argentina profile', () => {
      const csv = generateCSV(mockProducts, PRODUCT_EXPORT_COLUMNS, {
        locale: 'es-AR',
        profile: 'excelAR',
        includeHeaders: true,
      });

      const lines = csv.split('\n');
      
      // Check semicolon delimiter
      expect(lines[0]).toContain(';');
      expect(lines[1]).toContain(';');
      
      // Check BOM
      expect(csv.charCodeAt(0)).toBe(0xFEFF);
    });

    it('should generate CSV with English locale', () => {
      const csv = generateCSV(mockProducts, PRODUCT_EXPORT_COLUMNS, {
        locale: 'en-US',
        profile: 'standard',
        includeHeaders: true,
      });

      const lines = csv.split('\n');
      
      // Check data formatting
      expect(lines[1]).toContain('1500.50'); // Decimal dot for en-US
      expect(lines[1]).toContain('Yes'); // Boolean in English
    });

    it('should handle missing values', () => {
      const dataWithNulls = [
        {
          sku: 'PROD-001',
          name: null,
          category: undefined,
          retailPrice: 0,
          isActive: false,
        },
      ];

      const csv = generateCSV(dataWithNulls, PRODUCT_EXPORT_COLUMNS, {
        locale: 'es-AR',
        profile: 'standard',
        includeHeaders: true,
      });

      const lines = csv.split('\n');
      expect(lines[1]).toContain('PROD-001');
      expect(lines[1]).toContain(',,'); // Empty values for null/undefined
    });

    it('should escape CSV fields with special characters', () => {
      const dataWithSpecialChars = [
        {
          sku: 'PROD-001',
          name: 'Product with "quotes" and, commas',
          category: 'Category\nwith\nnewlines',
          retailPrice: 100.00,
        },
      ];

      const csv = generateCSV(dataWithSpecialChars, PRODUCT_EXPORT_COLUMNS, {
        locale: 'en-US',
        profile: 'standard',
        includeHeaders: true,
      });

      // Should wrap fields with special characters in quotes
      expect(csv).toContain('"Product with ""quotes"" and, commas"');
      expect(csv).toContain('"Category\nwith\nnewlines"');
    });
  });

  describe('generateExportFilename', () => {
    it('should generate filename with locale and timestamp', () => {
      const filename = generateExportFilename('products', 'es-AR', 'csv', true);
      
      expect(filename).toMatch(/^products_es_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.csv$/);
    });

    it('should generate filename without timestamp', () => {
      const filename = generateExportFilename('sales', 'en-US', 'pdf', false);
      
      expect(filename).toBe('sales_en.pdf');
    });

    it('should handle different extensions', () => {
      const csvFilename = generateExportFilename('test', 'es-AR', 'csv', false);
      const pdfFilename = generateExportFilename('test', 'es-AR', 'pdf', false);
      
      expect(csvFilename).toBe('test_es.csv');
      expect(pdfFilename).toBe('test_es.pdf');
    });
  });

  describe('getLocalizedColumns', () => {
    it('should localize column labels', () => {
      const translations = {
        sku: 'Código SKU',
        name: 'Nombre del Producto',
        category: 'Categoría',
      };

      const localizedColumns = getLocalizedColumns(
        PRODUCT_EXPORT_COLUMNS.slice(0, 3),
        'es-AR',
        translations
      );

      expect(localizedColumns[0].label).toBe('Código SKU');
      expect(localizedColumns[1].label).toBe('Nombre del Producto');
      expect(localizedColumns[2].label).toBe('Categoría');
    });

    it('should keep original labels for missing translations', () => {
      const translations = {
        sku: 'Código SKU',
        // name translation missing
      };

      const localizedColumns = getLocalizedColumns(
        PRODUCT_EXPORT_COLUMNS.slice(0, 2),
        'es-AR',
        translations
      );

      expect(localizedColumns[0].label).toBe('Código SKU');
      expect(localizedColumns[1].label).toBe('Nombre'); // Original label
    });
  });

  describe('validateExportData', () => {
    it('should validate correct data', () => {
      const result = validateExportData(mockProducts, PRODUCT_EXPORT_COLUMNS);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect empty data', () => {
      const result = validateExportData([], PRODUCT_EXPORT_COLUMNS);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('No data to export');
    });

    it('should detect invalid data type', () => {
      const result = validateExportData('not an array' as any, PRODUCT_EXPORT_COLUMNS);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Data must be an array');
    });

    it('should detect missing columns', () => {
      const result = validateExportData([], []);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Column definitions are required');
    });

    it('should detect missing columns in data', () => {
      const incompleteData = [{ sku: 'PROD-001' }]; // Missing other required columns
      
      const result = validateExportData(incompleteData, PRODUCT_EXPORT_COLUMNS);
      
      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('Missing columns in data');
    });
  });

  describe('CSV Profiles', () => {
    it('should have correct standard profile configuration', () => {
      const profile = CSV_PROFILES.standard;
      
      expect(profile.delimiter).toBe(',');
      expect(profile.decimal).toBe('.');
      expect(profile.bom).toBe(false);
    });

    it('should have correct Excel Argentina profile configuration', () => {
      const profile = CSV_PROFILES.excelAR;
      
      expect(profile.delimiter).toBe(';');
      expect(profile.decimal).toBe(',');
      expect(profile.bom).toBe(true);
    });
  });

  describe('Predefined Column Configurations', () => {
    it('should have product export columns', () => {
      expect(PRODUCT_EXPORT_COLUMNS).toHaveLength(8);
      expect(PRODUCT_EXPORT_COLUMNS[0].key).toBe('sku');
      expect(PRODUCT_EXPORT_COLUMNS[0].type).toBe('string');
    });

    it('should have sales export columns', () => {
      expect(SALES_EXPORT_COLUMNS).toHaveLength(6);
      expect(SALES_EXPORT_COLUMNS[0].key).toBe('number');
      expect(SALES_EXPORT_COLUMNS[3].type).toBe('currency');
    });

    it('should have correct column types', () => {
      const currencyColumns = PRODUCT_EXPORT_COLUMNS.filter(col => col.type === 'currency');
      const numberColumns = PRODUCT_EXPORT_COLUMNS.filter(col => col.type === 'number');
      const booleanColumns = PRODUCT_EXPORT_COLUMNS.filter(col => col.type === 'boolean');
      
      expect(currencyColumns).toHaveLength(2); // retailPrice, wholesalePrice
      expect(numberColumns).toHaveLength(2); // currentStock, minStock
      expect(booleanColumns).toHaveLength(1); // isActive
    });
  });

  describe('Date Formatting in Exports', () => {
    it('should format dates correctly for es-AR locale', () => {
      const csv = generateCSV(mockSales, SALES_EXPORT_COLUMNS, {
        locale: 'es-AR',
        profile: 'standard',
        includeHeaders: true,
      });

      // Should format date as dd/MM/yyyy for es-AR
      expect(csv).toContain('25/12/2023');
    });

    it('should format dates correctly for en-US locale', () => {
      const csv = generateCSV(mockSales, SALES_EXPORT_COLUMNS, {
        locale: 'en-US',
        profile: 'standard',
        includeHeaders: true,
      });

      // Should format date as MM/dd/yyyy for en-US
      expect(csv).toContain('12/25/2023');
    });
  });

  describe('Currency Formatting in Exports', () => {
    it('should format currency without symbols in CSV', () => {
      const csv = generateCSV(mockSales, SALES_EXPORT_COLUMNS, {
        locale: 'es-AR',
        profile: 'standard',
        includeHeaders: true,
      });

      // Should contain numeric values with proper decimal separator
      expect(csv).toContain('3000,00');
      expect(csv).toContain('1500,50');
      // Should not contain currency symbols
      expect(csv).not.toContain('$');
    });
  });
});

describe('Integration Tests', () => {
  describe('Full Export Workflow', () => {
    it('should export products with Spanish locale and Excel Argentina profile', () => {
      const csv = generateCSV(mockProducts, PRODUCT_EXPORT_COLUMNS, {
        locale: 'es-AR',
        profile: 'excelAR',
        includeHeaders: true,
      });

      const lines = csv.split('\n');
      
      // Verify structure
      expect(lines).toHaveLength(3); // Header + 2 data rows
      
      // Verify headers
      expect(lines[0]).toContain('SKU;Nombre;Categoría');
      
      // Verify data formatting
      expect(lines[1]).toContain('PROD-001;Producto Test;Categoría A;1500,50;1200,00;25;5;Sí');
      expect(lines[2]).toContain('PROD-002;Test Product;Category B;2750,75;2200,00;0;10;No');
      
      // Verify BOM
      expect(csv.charCodeAt(0)).toBe(0xFEFF);
    });

    it('should export sales with English locale and standard profile', () => {
      const csv = generateCSV(mockSales, SALES_EXPORT_COLUMNS, {
        locale: 'en-US',
        profile: 'standard',
        includeHeaders: true,
      });

      const lines = csv.split('\n');
      
      // Verify structure
      expect(lines).toHaveLength(3); // Header + 2 data rows
      
      // Verify comma delimiter
      expect(lines[0]).toContain('Sale Number,Sale Date,Customer Name');
      
      // Verify date and currency formatting
      expect(lines[1]).toContain('SALE-001,12/25/2023,Juan Pérez,3000.00,Efectivo,2');
      expect(lines[2]).toContain('SALE-002,12/25/2023,María García,1500.50,Tarjeta,1');
    });
  });
});
