import { test, expect } from '@playwright/test';

/**
 * E2E Tests for Internationalization
 * Tests the complete i18n flow including language switching and localized content
 */

test.describe('Internationalization (i18n)', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('/');
  });

  test('should display content in default locale (es-AR)', async ({ page }) => {
    // Check that the page loads with Spanish content
    await expect(page.locator('h1')).toContainText('Panel de Control');
    
    // Check navigation items are in Spanish
    await expect(page.locator('nav')).toContainText('Productos');
    await expect(page.locator('nav')).toContainText('Ventas');
    await expect(page.locator('nav')).toContainText('Inventario');
  });

  test('should switch language to English and update UI', async ({ page }) => {
    // Find and click the language switcher
    const languageSwitcher = page.locator('[data-testid="language-switcher"]');
    await languageSwitcher.click();
    
    // Select English
    await page.locator('[data-testid="locale-en-US"]').click();
    
    // Wait for the page to update
    await page.waitForTimeout(1000);
    
    // Check that content is now in English
    await expect(page.locator('h1')).toContainText('Dashboard');
    
    // Check navigation items are in English
    await expect(page.locator('nav')).toContainText('Products');
    await expect(page.locator('nav')).toContainText('Sales');
    await expect(page.locator('nav')).toContainText('Inventory');
  });

  test('should persist language preference across page reloads', async ({ page }) => {
    // Switch to English
    await page.locator('[data-testid="language-switcher"]').click();
    await page.locator('[data-testid="locale-en-US"]').click();
    await page.waitForTimeout(1000);
    
    // Reload the page
    await page.reload();
    
    // Check that English is still active
    await expect(page.locator('h1')).toContainText('Dashboard');
  });

  test('should format numbers according to locale', async ({ page }) => {
    // Navigate to a page with numbers (e.g., products or sales)
    await page.goto('/products');
    
    // Check that numbers are formatted with Spanish locale (dots for thousands, commas for decimals)
    const priceElements = page.locator('[data-testid="product-price"]');
    if (await priceElements.count() > 0) {
      const firstPrice = await priceElements.first().textContent();
      expect(firstPrice).toMatch(/\$.*\d{1,3}(\.\d{3})*,\d{2}/); // Spanish format: $1.234,56
    }
    
    // Switch to English
    await page.locator('[data-testid="language-switcher"]').click();
    await page.locator('[data-testid="locale-en-US"]').click();
    await page.waitForTimeout(1000);
    
    // Check that numbers are now formatted with English locale
    if (await priceElements.count() > 0) {
      const firstPrice = await priceElements.first().textContent();
      expect(firstPrice).toMatch(/\$\d{1,3}(,\d{3})*\.\d{2}/); // English format: $1,234.56
    }
  });

  test('should format dates according to locale', async ({ page }) => {
    // Navigate to a page with dates
    await page.goto('/sales');
    
    // Check date format in Spanish locale
    const dateElements = page.locator('[data-testid="sale-date"]');
    if (await dateElements.count() > 0) {
      const firstDate = await dateElements.first().textContent();
      // Spanish dates typically use dd/mm/yyyy or dd de mmm de yyyy
      expect(firstDate).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}\s+de\s+\w+\s+de\s+\d{4}/);
    }
    
    // Switch to English
    await page.locator('[data-testid="language-switcher"]').click();
    await page.locator('[data-testid="locale-en-US"]').click();
    await page.waitForTimeout(1000);
    
    // Check date format in English locale
    if (await dateElements.count() > 0) {
      const firstDate = await dateElements.first().textContent();
      // English dates typically use mm/dd/yyyy or mmm dd, yyyy
      expect(firstDate).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}|\w+\s+\d{1,2},\s+\d{4}/);
    }
  });

  test('should display error messages in correct language', async ({ page }) => {
    // Navigate to a form (e.g., add product)
    await page.goto('/products/new');
    
    // Try to submit form without required fields
    await page.locator('[data-testid="submit-button"]').click();
    
    // Check that error messages are in Spanish
    await expect(page.locator('[data-testid="error-message"]')).toContainText('requerido');
    
    // Switch to English
    await page.locator('[data-testid="language-switcher"]').click();
    await page.locator('[data-testid="locale-en-US"]').click();
    await page.waitForTimeout(1000);
    
    // Try to submit form again
    await page.locator('[data-testid="submit-button"]').click();
    
    // Check that error messages are now in English
    await expect(page.locator('[data-testid="error-message"]')).toContainText('required');
  });

  test('should handle currency display with dual format', async ({ page }) => {
    // Navigate to dashboard or sales page
    await page.goto('/dashboard');
    
    // Look for currency displays
    const currencyElements = page.locator('[data-testid="currency-display"]');
    if (await currencyElements.count() > 0) {
      const firstCurrency = await currencyElements.first().textContent();
      
      // Should show ARS as primary currency
      expect(firstCurrency).toContain('$');
      
      // May also show USD equivalent
      if (firstCurrency.includes('USD')) {
        expect(firstCurrency).toMatch(/\$.*ARS.*≈.*USD/);
      }
    }
  });

  test('should export reports in correct locale', async ({ page }) => {
    // Navigate to reports page
    await page.goto('/reports');
    
    // Generate a report
    await page.locator('[data-testid="generate-report"]').click();
    
    // Check that report headers are in Spanish
    await expect(page.locator('[data-testid="report-content"]')).toContainText('Total');
    await expect(page.locator('[data-testid="report-content"]')).toContainText('Fecha');
    
    // Switch to English
    await page.locator('[data-testid="language-switcher"]').click();
    await page.locator('[data-testid="locale-en-US"]').click();
    await page.waitForTimeout(1000);
    
    // Generate report again
    await page.locator('[data-testid="generate-report"]').click();
    
    // Check that report headers are now in English
    await expect(page.locator('[data-testid="report-content"]')).toContainText('Total');
    await expect(page.locator('[data-testid="report-content"]')).toContainText('Date');
  });

  test('should handle CSV export with locale-specific format', async ({ page }) => {
    // Navigate to a page with export functionality
    await page.goto('/products');
    
    // Start download for CSV export
    const downloadPromise = page.waitForEvent('download');
    await page.locator('[data-testid="export-csv"]').click();
    
    // Check that export options include locale-specific formats
    await expect(page.locator('[data-testid="csv-format-options"]')).toContainText('CSV Estándar');
    await expect(page.locator('[data-testid="csv-format-options"]')).toContainText('CSV Excel Argentina');
    
    // Select Excel Argentina format
    await page.locator('[data-testid="csv-excel-ar"]').click();
    
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toMatch(/\.csv$/);
  });

  test('should display KPIs and charts with localized labels', async ({ page }) => {
    // Navigate to dashboard
    await page.goto('/dashboard');
    
    // Check that KPI labels are in Spanish
    await expect(page.locator('[data-testid="kpi-labels"]')).toContainText('Ventas');
    await expect(page.locator('[data-testid="kpi-labels"]')).toContainText('Ganancia');
    
    // Check chart labels
    const chartLabels = page.locator('[data-testid="chart-labels"]');
    if (await chartLabels.count() > 0) {
      await expect(chartLabels).toContainText('Total');
    }
    
    // Switch to English
    await page.locator('[data-testid="language-switcher"]').click();
    await page.locator('[data-testid="locale-en-US"]').click();
    await page.waitForTimeout(1000);
    
    // Check that KPI labels are now in English
    await expect(page.locator('[data-testid="kpi-labels"]')).toContainText('Sales');
    await expect(page.locator('[data-testid="kpi-labels"]')).toContainText('Profit');
  });

  test('should handle time format according to locale', async ({ page }) => {
    // Navigate to a page with time displays
    await page.goto('/sales');
    
    // Check time format in Spanish locale (24h)
    const timeElements = page.locator('[data-testid="time-display"]');
    if (await timeElements.count() > 0) {
      const firstTime = await timeElements.first().textContent();
      // Should be 24h format (no AM/PM)
      expect(firstTime).toMatch(/\d{1,2}:\d{2}/);
      expect(firstTime).not.toMatch(/AM|PM/i);
    }
    
    // Switch to English
    await page.locator('[data-testid="language-switcher"]').click();
    await page.locator('[data-testid="locale-en-US"]').click();
    await page.waitForTimeout(1000);
    
    // Check time format in English locale (12h)
    if (await timeElements.count() > 0) {
      const firstTime = await timeElements.first().textContent();
      // Should be 12h format (with AM/PM)
      expect(firstTime).toMatch(/\d{1,2}:\d{2}\s*(AM|PM)/i);
    }
  });
});

test.describe('Day-in-the-Life E2E Test', () => {
  test('complete workflow in Spanish (es-AR)', async ({ page }) => {
    // Ensure we're in Spanish locale
    await page.goto('/');
    
    // 1. Add a product
    await page.goto('/products/new');
    await page.fill('[data-testid="product-name"]', 'Producto de Prueba');
    await page.fill('[data-testid="product-sku"]', 'TEST-001');
    await page.fill('[data-testid="product-price"]', '1.500,00');
    await page.click('[data-testid="submit-button"]');
    
    // Verify success message in Spanish
    await expect(page.locator('[data-testid="success-message"]')).toContainText('creado exitosamente');
    
    // 2. Receive purchase
    await page.goto('/purchases/new');
    await page.fill('[data-testid="supplier-name"]', 'Proveedor Test');
    await page.click('[data-testid="add-item"]');
    await page.selectOption('[data-testid="product-select"]', 'TEST-001');
    await page.fill('[data-testid="quantity"]', '10');
    await page.fill('[data-testid="unit-cost"]', '1.000,00');
    await page.click('[data-testid="submit-button"]');
    
    // 3. Make a sale
    await page.goto('/sales/new');
    await page.click('[data-testid="add-item"]');
    await page.selectOption('[data-testid="product-select"]', 'TEST-001');
    await page.fill('[data-testid="quantity"]', '2');
    await page.click('[data-testid="submit-button"]');
    
    // 4. Cash closing
    await page.goto('/cash/closing');
    await page.fill('[data-testid="opening-cash"]', '10.000,00');
    await page.fill('[data-testid="closing-cash"]', '13.000,00');
    await page.click('[data-testid="submit-button"]');
    
    // 5. Export dashboard
    await page.goto('/dashboard');
    await page.click('[data-testid="export-pdf"]');
    
    // Verify all text is in Spanish throughout the flow
    await expect(page.locator('body')).toContainText('Panel de Control');
  });

  test('complete workflow in English (en-US)', async ({ page }) => {
    // Switch to English locale
    await page.goto('/');
    await page.locator('[data-testid="language-switcher"]').click();
    await page.locator('[data-testid="locale-en-US"]').click();
    await page.waitForTimeout(1000);
    
    // 1. Add a product
    await page.goto('/products/new');
    await page.fill('[data-testid="product-name"]', 'Test Product');
    await page.fill('[data-testid="product-sku"]', 'TEST-002');
    await page.fill('[data-testid="product-price"]', '1,500.00');
    await page.click('[data-testid="submit-button"]');
    
    // Verify success message in English
    await expect(page.locator('[data-testid="success-message"]')).toContainText('created successfully');
    
    // 2. Receive purchase
    await page.goto('/purchases/new');
    await page.fill('[data-testid="supplier-name"]', 'Test Supplier');
    await page.click('[data-testid="add-item"]');
    await page.selectOption('[data-testid="product-select"]', 'TEST-002');
    await page.fill('[data-testid="quantity"]', '10');
    await page.fill('[data-testid="unit-cost"]', '1,000.00');
    await page.click('[data-testid="submit-button"]');
    
    // 3. Make a sale
    await page.goto('/sales/new');
    await page.click('[data-testid="add-item"]');
    await page.selectOption('[data-testid="product-select"]', 'TEST-002');
    await page.fill('[data-testid="quantity"]', '2');
    await page.click('[data-testid="submit-button"]');
    
    // 4. Cash closing
    await page.goto('/cash/closing');
    await page.fill('[data-testid="opening-cash"]', '10,000.00');
    await page.fill('[data-testid="closing-cash"]', '13,000.00');
    await page.click('[data-testid="submit-button"]');
    
    // 5. Export dashboard
    await page.goto('/dashboard');
    await page.click('[data-testid="export-pdf"]');
    
    // Verify all text is in English throughout the flow
    await expect(page.locator('body')).toContainText('Dashboard');
  });
});
