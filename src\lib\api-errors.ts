/**
 * Localized API Error Messages
 * Provides consistent error messages across the API with proper localization
 */

import { type Locale } from '@/i18n';

// Error code to message key mapping
const ERROR_MESSAGES = {
  // Authentication & Authorization
  unauthorized: {
    'es-AR': 'No autorizado. Por favor, inicie sesión.',
    'en-US': 'Unauthorized. Please log in.',
  },
  forbidden: {
    'es-AR': 'Acceso prohibido. No tiene permisos para realizar esta acción.',
    'en-US': 'Access forbidden. You do not have permission to perform this action.',
  },
  sessionExpired: {
    'es-AR': 'Su sesión ha expirado. Por favor, inicie sesión nuevamente.',
    'en-US': 'Your session has expired. Please log in again.',
  },

  // Validation Errors
  validation: {
    'es-AR': 'Error de validación. Verifique los datos ingresados.',
    'en-US': 'Validation error. Please check the entered data.',
  },
  required: {
    'es-AR': 'Este campo es requerido.',
    'en-US': 'This field is required.',
  },
  invalidFormat: {
    'es-AR': 'Formato inválido.',
    'en-US': 'Invalid format.',
  },
  invalidEmail: {
    'es-AR': 'Ingrese un correo electrónico válido.',
    'en-US': 'Please enter a valid email address.',
  },
  invalidLocale: {
    'es-AR': 'Idioma no válido.',
    'en-US': 'Invalid locale.',
  },

  // Resource Errors
  notFound: {
    'es-AR': 'Recurso no encontrado.',
    'en-US': 'Resource not found.',
  },
  userNotFound: {
    'es-AR': 'Usuario no encontrado.',
    'en-US': 'User not found.',
  },
  productNotFound: {
    'es-AR': 'Producto no encontrado.',
    'en-US': 'Product not found.',
  },
  categoryNotFound: {
    'es-AR': 'Categoría no encontrada.',
    'en-US': 'Category not found.',
  },

  // Business Logic Errors
  insufficientStock: {
    'es-AR': 'Stock insuficiente para completar la operación.',
    'en-US': 'Insufficient stock to complete the operation.',
  },
  duplicateEntry: {
    'es-AR': 'Ya existe un registro con estos datos.',
    'en-US': 'A record with this data already exists.',
  },
  duplicateSku: {
    'es-AR': 'Ya existe un producto con este SKU.',
    'en-US': 'A product with this SKU already exists.',
  },
  duplicateBarcode: {
    'es-AR': 'Ya existe un producto con este código de barras.',
    'en-US': 'A product with this barcode already exists.',
  },
  invalidPrice: {
    'es-AR': 'El precio debe ser mayor a cero.',
    'en-US': 'Price must be greater than zero.',
  },
  invalidQuantity: {
    'es-AR': 'La cantidad debe ser mayor a cero.',
    'en-US': 'Quantity must be greater than zero.',
  },
  invalidDateRange: {
    'es-AR': 'El rango de fechas es inválido.',
    'en-US': 'Invalid date range.',
  },

  // Server Errors
  serverError: {
    'es-AR': 'Error interno del servidor. Intente nuevamente más tarde.',
    'en-US': 'Internal server error. Please try again later.',
  },
  databaseError: {
    'es-AR': 'Error de base de datos. Intente nuevamente más tarde.',
    'en-US': 'Database error. Please try again later.',
  },
  networkError: {
    'es-AR': 'Error de conexión de red.',
    'en-US': 'Network connection error.',
  },
  timeout: {
    'es-AR': 'Tiempo de espera agotado.',
    'en-US': 'Request timeout.',
  },

  // Success Messages
  settingsSaved: {
    'es-AR': 'Configuración guardada exitosamente.',
    'en-US': 'Settings saved successfully.',
  },
  productCreated: {
    'es-AR': 'Producto creado exitosamente.',
    'en-US': 'Product created successfully.',
  },
  productUpdated: {
    'es-AR': 'Producto actualizado exitosamente.',
    'en-US': 'Product updated successfully.',
  },
  productDeleted: {
    'es-AR': 'Producto eliminado exitosamente.',
    'en-US': 'Product deleted successfully.',
  },
  saleCompleted: {
    'es-AR': 'Venta completada exitosamente.',
    'en-US': 'Sale completed successfully.',
  },
  purchaseCreated: {
    'es-AR': 'Compra creada exitosamente.',
    'en-US': 'Purchase created successfully.',
  },
  cashClosingCompleted: {
    'es-AR': 'Cierre de caja completado exitosamente.',
    'en-US': 'Cash closing completed successfully.',
  },
  reportGenerated: {
    'es-AR': 'Reporte generado exitosamente.',
    'en-US': 'Report generated successfully.',
  },
  importCompleted: {
    'es-AR': 'Importación completada exitosamente.',
    'en-US': 'Import completed successfully.',
  },
  exportCompleted: {
    'es-AR': 'Exportación completada exitosamente.',
    'en-US': 'Export completed successfully.',
  },

  // Import/Export Errors
  importError: {
    'es-AR': 'Error durante la importación.',
    'en-US': 'Error during import.',
  },
  invalidFileFormat: {
    'es-AR': 'Formato de archivo inválido.',
    'en-US': 'Invalid file format.',
  },
  fileTooLarge: {
    'es-AR': 'El archivo es demasiado grande.',
    'en-US': 'File is too large.',
  },
  emptyFile: {
    'es-AR': 'El archivo está vacío.',
    'en-US': 'File is empty.',
  },

  // Rate Limiting
  rateLimitExceeded: {
    'es-AR': 'Demasiadas solicitudes. Intente nuevamente más tarde.',
    'en-US': 'Too many requests. Please try again later.',
  },

  // Maintenance
  maintenanceMode: {
    'es-AR': 'El sistema está en mantenimiento. Intente nuevamente más tarde.',
    'en-US': 'System is under maintenance. Please try again later.',
  },
} as const;

export type ErrorCode = keyof typeof ERROR_MESSAGES;

/**
 * Get localized error message
 */
export function getLocalizedErrorMessage(
  code: ErrorCode,
  locale: Locale,
  fallbackLocale: Locale = 'en-US'
): string {
  const messages = ERROR_MESSAGES[code];
  
  if (!messages) {
    console.warn(`Unknown error code: ${code}`);
    return locale === 'es-AR' 
      ? 'Ha ocurrido un error inesperado.'
      : 'An unexpected error occurred.';
  }

  // Try to get message in requested locale
  let message = messages[locale];
  
  // Fallback to fallback locale if not found
  if (!message && locale !== fallbackLocale) {
    message = messages[fallbackLocale];
  }
  
  // Final fallback
  if (!message) {
    message = Object.values(messages)[0];
  }

  return message || 'Unknown error';
}

/**
 * Create standardized API error response
 */
export interface ApiErrorResponse {
  error: string;
  code: string;
  message?: string;
  details?: any;
  timestamp: string;
  path?: string;
}

export function createApiError(
  code: ErrorCode,
  locale: Locale,
  options?: {
    details?: any;
    path?: string;
    statusCode?: number;
  }
): ApiErrorResponse {
  const message = getLocalizedErrorMessage(code, locale);
  
  return {
    error: message,
    code: code.toUpperCase(),
    message,
    details: options?.details,
    timestamp: new Date().toISOString(),
    path: options?.path,
  };
}

/**
 * Validation error formatter
 */
export function formatValidationErrors(
  errors: Array<{ path: string[]; message: string }>,
  locale: Locale
): Record<string, string> {
  const formatted: Record<string, string> = {};
  
  errors.forEach((error) => {
    const field = error.path.join('.');
    // You could translate common validation messages here
    formatted[field] = error.message;
  });
  
  return formatted;
}

/**
 * HTTP status code to error code mapping
 */
export function getErrorCodeFromStatus(status: number): ErrorCode {
  switch (status) {
    case 400:
      return 'validation';
    case 401:
      return 'unauthorized';
    case 403:
      return 'forbidden';
    case 404:
      return 'notFound';
    case 408:
      return 'timeout';
    case 429:
      return 'rateLimitExceeded';
    case 500:
      return 'serverError';
    case 503:
      return 'maintenanceMode';
    default:
      return 'serverError';
  }
}

/**
 * Error boundary helper for API routes
 */
export function withErrorHandling<T extends any[], R>(
  handler: (...args: T) => Promise<R>,
  locale?: Locale
) {
  return async (...args: T): Promise<R> => {
    try {
      return await handler(...args);
    } catch (error) {
      console.error('API Error:', error);
      
      const effectiveLocale = locale || 'en-US';
      const errorCode = error instanceof Error && 'code' in error 
        ? (error as any).code as ErrorCode
        : 'serverError';
      
      throw createApiError(errorCode, effectiveLocale, {
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      });
    }
  };
}
